@tailwind base;
@tailwind components;
@tailwind utilities;

/* Custom moving gradient */
body {
    background: linear-gradient(to top, #0008ff, #002fff);
    /* blue-900 to blue-300 */
    background-size: 200% 200%;
    /* Make the gradient larger than the viewport */
    animation: moving-gradient 3s ease infinite;
    font-family: var(--font-helvetica), system-ui, sans-serif;
}

@keyframes spin {
    to {
        transform: rotate(360deg);
    }
}

.animate-spin {
    animation: spin 1s linear infinite;
}

.selected-badge {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background-color: white;
    border: 2px solid #dc2626;
    color: #dc2626;
    padding: 4px 16px;
    /* Increased padding */
    border-radius: 9999px;
    font-size: 1.5rem;
    /* Increased font size */
    font-weight: bold;
    display: flex;
    align-items: center;
    gap: 8px;
    z-index: 10;
}

@keyframes shine {
    0% {
        transform: translateX(-100%);
    }

    100% {
        transform: translateX(100%);
    }
}

.payment-element {
    min-height: 50px;
    padding: 12px;
    border: 1px solid #93c5fd;
    border-radius: 8px;
    background: white;
}

@media (max-width: 480px) {
    .payment-element {
        min-height: 40px;
        padding: 8px;
    }
}

.animate-shine {
    animation: shine 2s linear infinite;
    background-size: 200% auto;
    background-clip: text;
    -webkit-background-clip: text;
}

@layer utilities {
    .animate-moving-gradient {
        background-image: linear-gradient(45deg,
                #0048fe,
                #1100ff);
        background-size: 400% 400%;
    }

    /* New wave animation for the emoji */
    .wave-animation {
        animation: wave 2s infinite;
    }
}

@layer utilities {
    .grow-text {
        animation: grow 10s infinite;
    }
}

@keyframes grow {

    0%,
    10%,
    100% {
        font-size: 1rem;
        /* Normal size (text-base) */
    }

    5% {
        font-size: 1.125rem;
        /* Slightly larger (text-lg) */
    }
}

.scheduling-page {
    max-width: 800px;
    margin: 0 auto;
    font-family: var(--font-helvetica), system-ui, sans-serif;
    color: #f3f4f6;
    /* gray-100 */
}

.calendar-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.5rem;
    padding: 1rem;
    background: #1e3a8a;
    /* palatinate_blue-800 */
    border-radius: 8px;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
}

.calendar-header button {
    padding: 0.5rem 1.5rem;
    border: 2px solid #3b82f6;
    /* vivid_sky_blue-500 */
    border-radius: 6px;
    background: #1e3a8a;
    /* palatinate_blue-800 */
    color: #f3f4f6;
    /* gray-100 */
    cursor: pointer;
    transition: all 0.2s;
    font-weight: 500;
}

.calendar-header button:hover:not(:disabled) {
    background: #2563eb;
    /* vivid_sky_blue-600 */
    border-color: #60a5fa;
    /* vivid_sky_blue-400 */
}

.calendar-header button:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    border-color: #1e3a8a;
    /* palatinate_blue-800 */
}

.calendar-grid {
    display: grid;
    grid-template-columns: repeat(7, minmax(0, 1fr));
    gap: 2px;
    margin-bottom: 1.5rem;
    background: #1e3a8a;
    /* palatinate_blue-800 */
    padding: 4px;
    border-radius: 8px;
}

.calendar-day-header {
    text-align: center;
    padding: 1rem;
    background: #1e3a8a;
    /* palatinate_blue-800 */
    font-weight: 600;
    text-transform: uppercase;
    font-size: 0.875rem;
    color: #93c5fd;
    /* vivid_sky_blue-300 */
    border-bottom: 2px solid #3b82f6;
    /* vivid_sky_blue-500 */
}

.calendar-day {
    aspect-ratio: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    border: 2px solid #1e3a8a;
    /* palatinate_blue-800 */
    background: #2563eb;
    /* vivid_sky_blue-600 */
    color: #f3f4f6;
    /* gray-100 */
    cursor: pointer;
    transition: all 0.2s;
    font-weight: 500;
    border-radius: 4px;
}

.calendar-day:hover:not(.disabled) {
    background: #3b82f6;
    /* vivid_sky_blue-500 */
    transform: scale(1.05);
    z-index: 1;
}

.calendar-day.selected {
    background: #60a5fa;
    /* vivid_sky_blue-400 */
    color: #1e3a8a;
    /* palatinate_blue-800 */
    font-weight: 600;
    border-color: #93c5fd;
    /* vivid_sky_blue-300 */
}

.calendar-day.disabled {
    background: #1e3a8a;
    /* palatinate_blue-800 */
    color: #3b82f6;
    /* vivid_sky_blue-500 */
    opacity: 0.6;
    cursor: not-allowed;
}

.time-slots {
    margin-top: 2rem;
    background: #1e3a8a;
    /* palatinate_blue-800 */
    padding: 1.5rem;
    border-radius: 8px;
}

.time-slots h3 {
    color: #93c5fd;
    /* vivid_sky_blue-300 */
    margin-bottom: 1rem;
    font-size: 1.25rem;
}

.time-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
    gap: 0.75rem;
}

.time-slot {
    padding: 0.75rem;
    border: 2px solid #3b82f6;
    /* vivid_sky_blue-500 */
    border-radius: 6px;
    background: #2563eb;
    /* vivid_sky_blue-600 */
    color: #f3f4f6;
    /* gray-100 */
    cursor: pointer;
    transition: all 0.2s;
    text-align: center;
    font-weight: 500;
}

.time-slot:hover {
    background: #3b82f6;
    /* vivid_sky_blue-500 */
    transform: translateY(-2px);
}

.time-slot.selected {
    background: #60a5fa;
    /* vivid_sky_blue-400 */
    color: #1e3a8a;
    /* palatinate_blue-800 */
    border-color: #93c5fd;
    /* vivid_sky_blue-300 */
    font-weight: 600;
}

.slideshow-container {
    aspect-ratio: 16/9;
}

.slideshow-image {
    transition: opacity 1s ease-in-out;
}

@keyframes border-shine {
    from {
        background-position: 0 0;
    }

    to {
        background-position: 200% 0;
    }
}

.animate-border-shine {
    animation: border-shine 2s linear infinite;
}

/* New keyframes for the wave animation */
@keyframes wave {

    0%,
    100% {
        transform: rotate(0deg) scale(1);
    }

    25% {
        transform: rotate(20deg) scale(1.2);
    }

    75% {
        transform: rotate(-20deg) scale(0.8);
    }
}

.shiny-button::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(120deg,
            transparent 0%,
            transparent 45%,
            /* Start of the shine band */
            rgba(255, 255, 255, 0.5) 45%,
            rgba(255, 255, 255, 0.5) 55%,
            /* End of the shine band */
            transparent 55%,
            transparent 100%);
    background-size: 200% 100%;
    animation: shine 5s linear infinite;
    /* Linear timing for constant speed */
    z-index: 1;
    pointer-events: none;
}

@keyframes shine {
    0% {
        background-position: 100% 0;
    }

    100% {
        background-position: -100% 0;
    }
}

@layer base {
    :root {
        --background: 0 0% 100%;
        --foreground: 0 0% 3.9%;
        --card: 0 0% 100%;
        --card-foreground: 0 0% 3.9%;
        --popover: 0 0% 100%;
        --popover-foreground: 0 0% 3.9%;
        --primary: 0 0% 9%;
        --primary-foreground: 0 0% 98%;
        --secondary: 0 0% 96.1%;
        --secondary-foreground: 0 0% 9%;
        --muted: 0 0% 96.1%;
        --muted-foreground: 0 0% 45.1%;
        --accent: 0 0% 96.1%;
        --accent-foreground: 0 0% 9%;
        --destructive: 0 84.2% 60.2%;
        --destructive-foreground: 0 0% 98%;
        --border: 0 0% 89.8%;
        --input: 0 0% 89.8%;
        --ring: 0 0% 3.9%;
        --chart-1: 12 76% 61%;
        --chart-2: 173 58% 39%;
        --chart-3: 197 37% 24%;
        --chart-4: 43 74% 66%;
        --chart-5: 27 87% 67%;
        --radius: 0.5rem;
    }

    .dark {
        --background: 0 0% 3.9%;
        --foreground: 0 0% 98%;
        --card: 0 0% 3.9%;
        --card-foreground: 0 0% 98%;
        --popover: 0 0% 3.9%;
        --popover-foreground: 0 0% 98%;
        --primary: 0 0% 98%;
        --primary-foreground: 0 0% 9%;
        --secondary: 0 0% 14.9%;
        --secondary-foreground: 0 0% 98%;
        --muted: 0 0% 14.9%;
        --muted-foreground: 0 0% 63.9%;
        --accent: 0 0% 14.9%;
        --accent-foreground: 0 0% 98%;
        --destructive: 0 62.8% 30.6%;
        --destructive-foreground: 0 0% 98%;
        --border: 0 0% 14.9%;
        --input: 0 0% 14.9%;
        --ring: 0 0% 83.1%;
        --chart-1: 220 70% 50%;
        --chart-2: 160 60% 45%;
        --chart-3: 30 80% 55%;
        --chart-4: 280 65% 60%;
        --chart-5: 340 75% 55%;
    }
}

@layer base {
    * {
        @apply border-border;
    }

    body {
        @apply bg-background text-foreground;
    }
}

/* Helvetica font utility class */
.font-helvetica {
    font-family: var(--font-helvetica), system-ui, sans-serif;
}