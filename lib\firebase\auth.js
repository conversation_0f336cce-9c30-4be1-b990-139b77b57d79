import { auth, db } from "./firebase"; // Import db for Firestore access
import { GoogleAuthProvider, signInWithPopup, signOut } from "firebase/auth";
import { doc, getDoc, setDoc } from "firebase/firestore";

export const signInWithGoogle = async () => {
    const provider = new GoogleAuthProvider();
    provider.setCustomParameters({
        prompt: "select_account", // Forces account chooser every time
    });
    try {
        const result = await signInWithPopup(auth, provider);
        const user = result.user;

        // Check if user document exists in Firestore
        const userRef = doc(db, "users", user.uid);
        const userDoc = await getDoc(userRef);

        if (!userDoc.exists()) {
            // New user: create document with default wage
            await setDoc(userRef, {
                userId: user.uid,
                email: user.email,
                displayName: user.displayName,
                wage: 16.00, // Default wage of $16/hr
                isAdmin: false, // Default to non-admin
            });
            console.log(`Created new user document for ${user.displayName} with wage $16/hr`);
        }
    } catch (error) {
        console.error("Google Sign-In Error:", error);
        throw error; // Re-throw to handle in UI if needed
    }
};

export const firebaseSignOut = () => signOut(auth);