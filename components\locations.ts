// components/locations.ts
export interface Branch {
    slug: string;
    businessNumber: string;
    location: string;
    reviewLink: string;
}

export const locations: Record<string, Branch> = {
    lawrence: {
        slug: 'lawrence',
        businessNumber: "+***********",
        location: "Lawrence",
        reviewLink: "https://g.page/r/CS98X9jMS0IREBM/review",
    },
    kansascity: {
        slug: 'kansascity',
        businessNumber: "+***********",
        location: "Kansas City",
        reviewLink: "https://g.page/r/CaNuJ0ypIXA7EBM/review",
    },
    stlouis: {
        slug: 'stlouis',
        businessNumber: "+***********",
        location: "St.Louis",
        reviewLink: "https://g.page/r/CaNuJ0ypIXA7EBM/review",
    },
    denver: {
        slug: 'denver',
        businessNumber: "+***********",
        location: "Denver, CO",
        reviewLink: "https://g.page/r/CaNuJ0ypIXA7EBM/review",
    },
    newyork: {
        slug: 'ny',
        businessNumber: "+***********",
        location: "New York",
        reviewLink: "https://g.page/r/CaNuJ0ypIXA7EBM/review",
    },
    la: {
        slug: 'la',
        businessNumber: "+***********",
        location: "Los Angeles",
        reviewLink: "https://g.page/r/CaNuJ0ypIXA7EBM/review",
    }
} as const;