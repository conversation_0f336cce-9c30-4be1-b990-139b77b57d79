import { ImageResponse } from "next/og";

export const alt = "Auto Detailing Services | Detail On The Go";
export const contentType = "image/png";
export const size = { width: 1200, height: 630 };

export default function OG() {
  return new ImageResponse(
    (
      <div
        style={{
          height: "100%",
          width: "100%",
          display: "flex",
          flexDirection: "column",
          alignItems: "center",
          justifyContent: "center",
          backgroundColor: "#e6f0fa",
          backgroundImage: "linear-gradient(135deg, #e6f0fa 60%, #b3d8f8 100%)",
        }}
      >
        <div
          style={{
            fontSize: "56px",
            fontWeight: "bold",
            color: "#003366",
            marginBottom: "24px",
            letterSpacing: "-1px",
          }}
        >
          Auto Detailing Services
        </div>
        <div
          style={{
            fontSize: "30px",
            color: "#1a3a5d",
            textAlign: "center",
            maxWidth: "80%",
          }}
        >
          Mobile car, boat, and RV detailing at your location
        </div>
        <div
          style={{
            fontSize: "22px",
            color: "#2d5c88",
            marginTop: "32px",
            fontWeight: "500",
          }}
        >
          Detail On The Go
        </div>
      </div>
    ),
    size
  );
}
