// app/not-found.tsx
'use client';

import { useEffect } from 'react';
import { useRouter } from 'next/navigation';

export default function NotFound() {
    const router = useRouter();

    useEffect(() => {
        const timeout = setTimeout(() => {
            router.replace('/');
        }, 2000); // Wait 2 seconds before redirecting

        return () => clearTimeout(timeout);
    }, [router]);

    return (
        <div>
            <h1 className="text-3xl font-bold mb-2">Finding your way back home...</h1>
        </div>
    );
}
