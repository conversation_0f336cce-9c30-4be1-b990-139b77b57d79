import { NextRequest, NextResponse } from 'next/server';

export async function POST(request: NextRequest) {
  try {
    const formData = await request.json();
    
    // Here you would typically:
    // 1. Validate the form data
    // 2. Save to database
    // 3. Send confirmation email to customer
    // 4. Send notification email to Peak Services
    
    // For now, we'll just log the data and return success
    console.log('Cleaning service request received:', {
      customer: `${formData.firstName} ${formData.lastName}`,
      email: formData.email,
      phone: formData.phone,
      address: `${formData.streetAddress}, ${formData.city}, ${formData.state} ${formData.zipCode}`,
      propertyType: formData.propertyType,
      frequency: formData.frequency,
      additionalServices: formData.additionalServices,
      timestamp: new Date().toISOString()
    });

    // Simulate processing time
    await new Promise(resolve => setTimeout(resolve, 1000));

    return NextResponse.json({ 
      success: true, 
      message: 'Request submitted successfully' 
    });
    
  } catch (error) {
    console.error('Error processing cleaning request:', error);
    return NextResponse.json(
      { success: false, message: 'Failed to process request' },
      { status: 500 }
    );
  }
}
