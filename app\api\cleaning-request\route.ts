import { NextRequest, NextResponse } from 'next/server';

export async function POST(request: NextRequest) {
  try {
    const formData = await request.json();

    // Log the request
    console.log('Cleaning service request received:', {
      customer: `${formData.firstName} ${formData.lastName}`,
      email: formData.email,
      phone: formData.phone,
      address: `${formData.streetAddress}, ${formData.city}, ${formData.state} ${formData.zipCode}`,
      propertyType: formData.propertyType,
      frequency: formData.frequency,
      additionalServices: formData.additionalServices,
      timestamp: new Date().toISOString()
    });

    // Send emails to customer and business owner
    try {
      const emailResponse = await fetch(`${request.nextUrl.origin}/api/cleaning-request-email`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(formData),
      });

      if (!emailResponse.ok) {
        console.error('Failed to send emails:', await emailResponse.text());
        // Continue processing even if email fails
      } else {
        console.log('Emails sent successfully');
      }
    } catch (emailError) {
      console.error('Email sending error:', emailError);
      // Continue processing even if email fails
    }

    // Simulate processing time
    await new Promise(resolve => setTimeout(resolve, 1000));

    return NextResponse.json({
      success: true,
      message: 'Request submitted successfully'
    });

  } catch (error) {
    console.error('Error processing cleaning request:', error);
    return NextResponse.json(
      { success: false, message: 'Failed to process request' },
      { status: 500 }
    );
  }
}
