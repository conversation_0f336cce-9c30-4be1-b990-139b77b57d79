import { Metadata } from 'next';

export const metadata: Metadata = {
    title: 'Frequently Asked Questions | Peak Services AK',
    description: 'Find answers to common questions about Peak Services AK cleaning services, booking, pricing, and more. Serving Ketchikan Alaska and Southeast Alaska.',
    keywords: [
        'FAQ',
        'frequently asked questions',
        'cleaning services FAQ',
        'Peak Services AK help',
        'cleaning company questions',
        'Ketchikan Alaska cleaning',
        'residential cleaning FAQ',
        'commercial cleaning FAQ'
    ],
    alternates: {
        canonical: 'https://peakservicesak.com/faq/',
    },
    openGraph: {
        title: 'Frequently Asked Questions | Peak Services AK',
        description: 'Answers to common questions about our professional cleaning services, booking, and more.',
        url: 'https://peakservicesak.com/faq/',
        images: [
            {
                url: '/megan owner of peak services.png',
                width: 1200,
                height: 630,
                alt: 'FAQ - Peak Services AK'
            }
        ],
        type: 'website',
    },
    twitter: {
        card: 'summary_large_image',
        title: 'Frequently Asked Questions | Peak Services AK',
        description: 'Answers to common questions about our professional cleaning services, booking, and more.',
        images: ['/megan owner of peak services.png'],
    }
};

export default function Layout({ children }: { children: React.ReactNode }) {
    return (
        <>
            {/* Structured Data for SEO */}
            <script
                type="application/ld+json"
                dangerouslySetInnerHTML={{
                    __html: JSON.stringify({
                        "@context": "https://schema.org",
                        "@type": "FAQPage",
                        "mainEntity": [
                            {
                                "@type": "Question",
                                "name": "What cleaning services do you offer?",
                                "acceptedAnswer": {
                                    "@type": "Answer",
                                    "text": "Peak Services AK offers comprehensive residential and commercial cleaning services including regular housekeeping, deep cleaning, move-in/move-out cleaning, vacation rental turnarounds, carpet shampooing, window washing, dryer vent cleaning, pressure washing, post-construction cleanup, industrial cleanup, and grounds keeping."
                                }
                            },
                            {
                                "@type": "Question",
                                "name": "What areas do you serve?",
                                "acceptedAnswer": {
                                    "@type": "Answer",
                                    "text": "We serve Ketchikan Alaska and all of Southeast Alaska with professional cleaning services."
                                }
                            },
                            {
                                "@type": "Question",
                                "name": "How do I get a quote?",
                                "acceptedAnswer": {
                                    "@type": "Answer",
                                    "text": "You can get a quote by calling us at (*************, emailing <EMAIL>, or using our online service request form."
                                }
                            }
                        ],
                        "provider": {
                            "@type": "LocalBusiness",
                            "name": "Peak Services AK",
                            "url": "https://peakservicesak.com/",
                            "telephone": "******-821-1335",
                            "email": "<EMAIL>"
                        }
                    })
                }}
            />
            {children}
        </>
    );
}
