/** @type {import('tailwindcss').Config} */
const plugin = require("tailwindcss/plugin");

module.exports = {
	darkMode: ["class"],
	content: ["./app/**/*.{js,ts,jsx,tsx}", "./components/**/*.{js,ts,jsx,tsx}"],
	future: {
		hoverOnlyWhenSupported: true,
	},
	theme: {
		extend: {
			fontFamily: {
				sans: [
					'var(--font-helvetica)',
					'system-ui',
					'sans-serif'
				],
				display: [
					'var(--font-helvetica)',
					'system-ui',
					'sans-serif'
				],
				default: [
					'var(--font-helvetica)',
					'system-ui',
					'sans-serif'
				],
				helvetica: [
					'var(--font-helvetica)',
					'system-ui',
					'sans-serif'
				]
			},
			colors: {
				yellow: '#f7ff00',
				ultra_pink: '#fa61ff',
				vivid_sky_blue: '#07c8f9',
				picton_blue: '#09a6f3',
				bleu_de_france: '#0a85ed',
				royal_blue: '#0c63e7',
				palatinate_blue: '#0d41e1',
				background: 'hsl(var(--background))',
				foreground: 'hsl(var(--foreground))',
				card: {
					DEFAULT: 'hsl(var(--card))',
					foreground: 'hsl(var(--card-foreground))'
				},
				popover: {
					DEFAULT: 'hsl(var(--popover))',
					foreground: 'hsl(var(--popover-foreground))'
				},
				primary: {
					DEFAULT: 'hsl(var(--primary))',
					foreground: 'hsl(var(--primary-foreground))'
				},
				secondary: {
					DEFAULT: 'hsl(var(--secondary))',
					foreground: 'hsl(var(--secondary-foreground))'
				},
				muted: {
					DEFAULT: 'hsl(var(--muted))',
					foreground: 'hsl(var(--muted-foreground))'
				},
				accent: {
					DEFAULT: 'hsl(var(--accent))',
					foreground: 'hsl(var(--accent-foreground))'
				},
				destructive: {
					DEFAULT: 'hsl(var(--destructive))',
					foreground: 'hsl(var(--destructive-foreground))'
				},
				border: 'hsl(var(--border))',
				input: 'hsl(var(--input))',
				ring: 'hsl(var(--ring))',
				chart: {
					'1': 'hsl(var(--chart-1))',
					'2': 'hsl(var(--chart-2))',
					'3': 'hsl(var(--chart-3))',
					'4': 'hsl(var(--chart-4))',
					'5': 'hsl(var(--chart-5))'
				}
			},
			animation: {
				'fade-up': 'fade-up 0.5s',
				'fade-down': 'fade-down 0.5s',
				'slide-up-fade': 'slide-up-fade 0.3s cubic-bezier(0.16, 1, 0.3, 1)',
				'slide-down-fade': 'slide-down-fade 0.3s cubic-bezier(0.16, 1, 0.3, 1)',
				'moving-gradient': 'moving-gradient 15s ease infinite'
			},
			keyframes: {
				'fade-up': {
					'0%': {
						opacity: 0,
						transform: 'translateY(10px)'
					},
					'80%': {
						opacity: 0.6
					},
					'100%': {
						opacity: 1,
						transform: 'translateY(0px)'
					}
				},
				'fade-down': {
					'0%': {
						opacity: 0,
						transform: 'translateY(-10px)'
					},
					'80%': {
						opacity: 0.6
					},
					'100%': {
						opacity: 1,
						transform: 'translateY(0px)'
					}
				},
				'slide-up-fade': {
					'0%': {
						opacity: 0,
						transform: 'translateY(6px)'
					},
					'100%': {
						opacity: 1,
						transform: 'translateY(0)'
					}
				},
				'slide-down-fade': {
					'0%': {
						opacity: 0,
						transform: 'translateY(-6px)'
					},
					'100%': {
						opacity: 1,
						transform: 'translateY(0)'
					}
				},
				'moving-gradient': {
					'0%': {
						backgroundPosition: '0% 0%'
					},
					'50%': {
						backgroundPosition: '100% 100%'
					},
					'100%': {
						backgroundPosition: '0% 0%'
					}
				}
			},
			borderRadius: {
				lg: 'var(--radius)',
				md: 'calc(var(--radius) - 2px)',
				sm: 'calc(var(--radius) - 4px)'
			}
		}
	},
	plugins: [
		require("@tailwindcss/forms"),
		require("@tailwindcss/typography"),
		plugin(({ addVariant }) => {
			addVariant("radix-side-top", '&[data-side="top"]');
			addVariant("radix-side-bottom", '&[data-side="bottom"]');
		}),
		require("tailwindcss-animate")
	],
};