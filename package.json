{"name": "precedent", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "format:write": "prettier --write \"**/*.{css,js,json,jsx,ts,tsx}\"", "format": "prettier \"**/*.{css,js,json,jsx,ts,tsx}\"", "start": "next start", "lint": "next lint"}, "dependencies": {"@clerk/nextjs": "^5.6.2", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-dialog": "^1.0.5", "@radix-ui/react-popover": "^1.0.7", "@radix-ui/react-tooltip": "^1.0.7", "@react-google-maps/api": "^2.20.6", "@stripe/react-stripe-js": "^3.7.0", "@stripe/stripe-js": "^6.1.0", "@types/node": "18.11.18", "@types/nodemailer": "^6.4.17", "@types/react": "18.2.24", "@types/react-dom": "18.2.8", "@vercel/analytics": "^0.1.11", "@vercel/og": "^0.0.26", "classnames": "^2.3.2", "clsx": "^2.1.1", "date-fns": "^4.1.0", "eslint": "8.31.0", "eslint-config-next": "13.1.1", "firebase": "^11.4.0", "focus-trap-react": "^10.2.2", "framer-motion": "^8.5.5", "lodash.debounce": "^4.0.8", "lucide-react": "0.105.0-alpha.4", "luxon": "^3.6.0", "ms": "^2.1.3", "next": "^14.2.25", "nodemailer": "^7.0.5", "react": "18.3.1", "react-dom": "18.3.1", "react-firebase-hooks": "^5.1.1", "react-markdown": "^8.0.7", "stripe": "^18.1.0", "tailwind-merge": "^1.14.0", "typescript": "5.1.6", "use-debounce": "^9.0.4", "vaul": "^0.6.8"}, "devDependencies": {"@tailwindcss/forms": "^0.5.6", "@tailwindcss/line-clamp": "^0.4.4", "@tailwindcss/typography": "^0.5.10", "@types/google.maps": "^3.58.1", "@types/lodash.debounce": "^4.0.9", "@types/luxon": "^3.4.2", "@types/ms": "^0.7.32", "autoprefixer": "^10.4.16", "concurrently": "^7.6.0", "postcss": "^8.4.31", "prettier": "^2.8.8", "prettier-plugin-tailwindcss": "^0.2.8", "tailwindcss": "^3.3.3", "tailwindcss-animate": "^1.0.7"}, "packageManager": "pnpm@9.3.0+sha512.ee7b93e0c2bd11409c6424f92b866f31d3ea1bef5fbe47d3c7500cdc3c9668833d2e55681ad66df5b640c61fa9dc25d546efa54d76d7f8bf54b13614ac293631"}