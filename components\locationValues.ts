interface Branch {
    branch: string;
    businessNumber: string;
    calendarId: string;
    collectionId: string;
    location: string;
    link: string;
    introMp3: string;
    allowedEmails: string[];
    employeeName: string;
    employeeNumber: string;
    reviewLink: string;
    lat: number;
    lng: number;
}

export const locationValues: Record<string, Branch> = {
    "+***********": {
        branch: "lwr",
        businessNumber: "+***********",
        calendarId: '<EMAIL>',
        collectionId: "sms-lwr",
        location: "Lawrence/KC and the surrounding areas",
        link: "https://www.detailongo.com/#lwrphone",
        introMp3: "https://blue-cat-8947.twil.io/assets/intro-lwr.mp3",
        allowedEmails: ["<EMAIL>", "<EMAIL>", "<EMAIL>"],
        employeeName: "<PERSON>",
        employeeNumber: "+***********",
        reviewLink: "https://g.page/r/CS98X9jMS0IREBM/review",
        lat: 38.9717,
        lng: -95.2353,
    },
    "+***********": {
        branch: "w-stl",
        businessNumber: "+***********",
        calendarId: '<EMAIL>',
        collectionId: "sms-w-stl",
        location: "St. Louis and the surrounding areas",
        link: "https://www.detailongo.com/#stlphone",
        introMp3: "https://blue-cat-8947.twil.io/assets/intro-stl.mp3",
        allowedEmails: ["<EMAIL>"],
        employeeName: "Taylor Woods",
        employeeNumber: "+3143689339",
        reviewLink: "https://g.page/r/CaNuJ0ypIXA7EBM/review",
        lat: 38.6270,
        lng: -90.1994,
    },
    "+***********": {
        branch: "la",
        businessNumber: "+***********",
        calendarId: '<EMAIL>',
        collectionId: "sms-la",
        location: "Los Angeles and the surrounding areas",
        link: "https://www.detailongo.com/#laphone",
        introMp3: "https://blue-cat-8947.twil.io/assets/intro-la.mp3",
        allowedEmails: ["<EMAIL>"],
        employeeName: "Jenny Biz",
        employeeNumber: "+***********",
        reviewLink: "https://g.page/r/CY3Q12skg1wsEBM/review",
        lat: 34.0522,
        lng: -118.2437,
    },
    "+***********": {
        branch: "dvr",
        businessNumber: "+***********",
        calendarId: '<EMAIL>',
        collectionId: "sms-dvr",
        location: "Denver and the surrounding areas",
        link: "https://www.detailongo.com/#dvrphone",
        introMp3: "https://blue-cat-8947.twil.io/assets/intro-dvr.mp3",
        allowedEmails: ["<EMAIL>"],
        employeeName: "Alexis Orona",
        employeeNumber: "+***********",
        reviewLink: "https://g.page/r/CY3Q12skg1wsEBM/review",
        lat: 39.7392,
        lng: -104.9903,
    },
    "+***********": {
        branch: "ny",
        businessNumber: "+***********",
        calendarId: '<EMAIL>',
        collectionId: "sms-ny",
        location: "New York and the surrounding areas",
        link: "https://www.detailongo.com/#nyphone",
        introMp3: "https://blue-cat-8947.twil.io/assets/intro-ny.mp3",
        allowedEmails: ["<EMAIL>"],
        employeeName: "Levi Taylor",
        employeeNumber: "+***********",
        reviewLink: "https://g.page/r/CTVgBNWsI5ZKEBM/review",
        lat: 40.7128,
        lng: -74.0060,
    },
};