"use client";

import cx from "classnames";
import Link from "next/link";

const ContactPage = () => {
    return (
        <div
            className={cx(
                "pt-20 flex flex-col items-center min-h-screen space-y-8 p-4 sm:p-8 bg-transparent text-gray-100 font-sans"
            )}
        >
            {/* Header */}
            <div className="text-center py-10 px-6">
                <h1 className="text-4xl md:text-6xl font-display font-bold text-white tracking-tight">
                    Contact Us
                </h1>
                <p className="mt-6 text-lg text-white/90 md:text-xl">
                    Get in touch with Detail On The Go
                </p>
            </div>

            {/* Contact Information */}
            <div className="w-full max-w-6xl mx-auto mt-10 space-y-6 bg-[rgba(15,55,255,0.9)] backdrop-blur-sm rounded-xl p-6">

                {/* Main Contact Info */}
                <div className="text-center space-y-6 py-8">
                    <div className="space-y-4">
                        <h2 className="text-2xl font-bold text-white">
                            We're Here to Help! 📧
                        </h2>
                        <p className="text-lg text-gray-300 max-w-2xl mx-auto">
                            Have questions, concerns, or special requests? We'd love to hear from you and provide the best possible service.
                        </p>
                    </div>

                    {/* Email Contact */}
                    <div className="bg-[rgba(15,55,255,0.9)] backdrop-blur-sm p-6 rounded-xl border border-white/30">
                        <div className="flex items-center justify-center space-x-3 mb-4">
                            <span className="text-3xl">✉️</span>
                            <h3 className="text-xl font-semibold text-white">Email Us</h3>
                        </div>
                        <a
                            href="mailto:<EMAIL>"
                            className="text-pink-400 hover:text-pink-300 transition-colors text-lg font-semibold"
                        >
                            <EMAIL>
                        </a>
                        <p className="text-gray-300 mt-2">
                            We typically respond within 24 hours
                        </p>
                    </div>

                    {/* Response Time */}
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-8">
                        <div className="bg-[rgba(15,55,255,0.9)] backdrop-blur-sm p-6 rounded-xl border border-white/30">
                            <div className="flex items-center justify-center space-x-2 mb-3">
                                <span className="text-2xl">⚡</span>
                                <h4 className="text-lg font-semibold text-white">Quick Response</h4>
                            </div>
                            <p className="text-gray-300 text-sm">
                                Most emails are answered within a few hours during business hours
                            </p>
                        </div>

                        <div className="bg-[rgba(15,55,255,0.9)] backdrop-blur-sm p-6 rounded-xl border border-white/30">
                            <div className="flex items-center justify-center space-x-2 mb-3">
                                <span className="text-2xl">🤝</span>
                                <h4 className="text-lg font-semibold text-white">Personal Service</h4>
                            </div>
                            <p className="text-gray-300 text-sm">
                                Every message gets personal attention from our team
                            </p>
                        </div>
                    </div>
                </div>

                {/* FAQ Reference */}
                <div className="mt-12 bg-[rgba(15,55,255,0.9)] backdrop-blur-sm p-8 rounded-xl text-center border border-white/30">
                    <div className="space-y-4">
                        <h2 className="text-2xl font-bold text-white flex items-center justify-center space-x-2">
                            <span>❓</span>
                            <span>Quick Answers</span>
                        </h2>
                        <p className="text-gray-300 max-w-2xl mx-auto">
                            Looking for quick answers? Check out our frequently asked questions for instant help with common inquiries about our services, pricing, and policies.
                        </p>
                        <Link
                            href="/faq"
                            className="inline-block bg-pink-500 text-white px-6 py-3 rounded-lg font-semibold hover:bg-pink-600 transition-colors transform hover:scale-105"
                        >
                            Visit Our FAQ Page →
                        </Link>
                    </div>
                </div>

                {/* Contact Types */}
                <div className="mt-8 grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div className="bg-[rgba(15,55,255,0.9)] backdrop-blur-sm p-4 rounded-xl border border-white/30 text-center">
                        <span className="text-2xl mb-2 block">📅</span>
                        <h4 className="text-white font-semibold mb-1">Booking Issues</h4>
                        <p className="text-gray-300 text-sm">Schedule changes, cancellations, or booking problems</p>
                    </div>

                    <div className="bg-[rgba(15,55,255,0.9)] backdrop-blur-sm p-4 rounded-xl border border-white/30 text-center">
                        <span className="text-2xl mb-2 block">💼</span>
                        <h4 className="text-white font-semibold mb-1">Service Questions</h4>
                        <p className="text-gray-300 text-sm">Questions about our detailing packages and services</p>
                    </div>

                    <div className="bg-[rgba(15,55,255,0.9)] backdrop-blur-sm p-4 rounded-xl border border-white/30 text-center">
                        <span className="text-2xl mb-2 block">💬</span>
                        <h4 className="text-white font-semibold mb-1">General Inquiries</h4>
                        <p className="text-gray-300 text-sm">Any other questions or feedback you'd like to share</p>
                    </div>
                </div>
            </div>
        </div>
    );
};

export default ContactPage;