import { Metadata } from 'next';

export const metadata: Metadata = {
    title: 'Cleaning Services | Peak Services AK - Ketchikan Alaska',
    description: 'Professional residential and commercial cleaning services in Ketchikan Alaska. Carpet shampooing, window washing, vacation rental turnarounds, move-in/out cleanings, dryer vent cleaning, pressure washing, post-construction cleanup, industrial cleanup, and grounds keeping.',
    keywords: [
        'cleaning services',
        'residential cleaning',
        'commercial cleaning',
        'carpet shampooing',
        'window washing',
        'vacation rental cleaning',
        'move-in move-out cleaning',
        'dryer vent cleaning',
        'pressure washing',
        'post-construction cleanup',
        'industrial cleanup',
        'grounds keeping',
        'Ketchikan Alaska',
        'Peak Services AK',
        '<PERSON>'
    ],
    alternates: {
        canonical: 'https://peakservicesak.com/services/',
    },
    openGraph: {
        title: 'Cleaning Services | Peak Services AK - Ketchikan Alaska',
        description: 'Professional residential and commercial cleaning services in Ketchikan Alaska. Comprehensive cleaning solutions for homes, businesses, and industrial spaces.',
        url: 'https://peakservicesak.com/services/',
        images: [
            {
                url: '/office cleaning.png',
                width: 1200,
                height: 630,
                alt: 'Peak Services AK Cleaning Services'
            }
        ],
        type: 'website',
    },
    twitter: {
        card: 'summary_large_image',
        title: 'Cleaning Services | Peak Services AK - Ketchikan Alaska',
        description: 'Professional residential and commercial cleaning services in Ketchikan Alaska.',
        images: ['/office cleaning.png'],
    }
};

// Add the required default export for the layout
export default function Layout({ children }: { children: React.ReactNode }) {
    return (
        <>
            {/* Structured Data for SEO */}
            <script
                type="application/ld+json"
                dangerouslySetInnerHTML={{
                    __html: JSON.stringify({
                        "@context": "https://schema.org",
                        "@type": "Service",
                        "name": "Professional Cleaning Services",
                        "provider": [
                            {
                                "@type": "LocalBusiness",
                                "name": "Peak Services AK",
                                "url": "https://peakservicesak.com/",
                                "image": "https://peakservicesak.com/office cleaning.png",
                                "telephone": "******-821-1335",
                                "email": "<EMAIL>",
                                "founder": {
                                    "@type": "Person",
                                    "name": "Megan Olmstead"
                                },
                                "foundingDate": "2022",
                                "address": {
                                    "@type": "PostalAddress",
                                    "addressLocality": "Ketchikan",
                                    "addressRegion": "AK",
                                    "addressCountry": "USA"
                                },
                                "areaServed": {
                                    "@type": "Place",
                                    "name": "Southeast Alaska"
                                }
                            },
                            {
                                "@type": "LocalBusiness",
                                "name": "Detail On The Go - Kansas City",
                                "url": "https://detailongo.com/",
                                "image": "https://detailongo.com/images/services-og.jpg",
                                "telephone": "******-555-1234",
                                "address": {
                                    "@type": "PostalAddress",
                                    "streetAddress": "123 Main St",
                                    "addressLocality": "Kansas City",
                                    "addressRegion": "MO",
                                    "postalCode": "64101",
                                    "addressCountry": "USA"
                                }
                            },
                            {
                                "@type": "LocalBusiness",
                                "name": "Detail On The Go - Topeka",
                                "url": "https://detailongo.com/",
                                "image": "https://detailongo.com/images/services-og.jpg",
                                "telephone": "******-555-5678",
                                "address": {
                                    "@type": "PostalAddress",
                                    "streetAddress": "456 Elm St",
                                    "addressLocality": "Topeka",
                                    "addressRegion": "KS",
                                    "postalCode": "66601",
                                    "addressCountry": "USA"
                                }
                            }
                            // ...add more franchise locations as needed...
                        ],
                        "areaServed": [
                            "Lawrence, KS",
                            "Kansas City, MO",
                            "Topeka, KS"
                            // ...add more areas as needed...
                        ],
                        "serviceType": [
                            "Car Detailing",
                            "Boat Detailing",
                            "RV Detailing",
                            "Ceramic Coating"
                        ],
                        "url": "https://detailongo.com/services/"
                    })
                }}
            />
            {children}
        </>
    );
}