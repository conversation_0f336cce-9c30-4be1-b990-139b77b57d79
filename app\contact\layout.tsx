import { Metadata } from 'next';

export const metadata: Metadata = {
    title: 'Contact Us | Detail On The Go',
    description: 'Contact Detail On The Go for mobile auto detailing services, franchise inquiries, or customer support. Call ************ or use our online form.',
    keywords: [
        'contact',
        'contact Detail On The Go',
        'auto detailing contact',
        'mobile detailing contact',
        'customer support',
        'franchise inquiry'
    ],
    alternates: {
        canonical: 'https://detailongo.com/contact/',
    },
    openGraph: {
        title: 'Contact Us | Detail On The Go',
        description: 'Get in touch with Detail On The Go for mobile detailing services or franchise opportunities.',
        url: 'https://detailongo.com/contact/',
        images: [
            {
                url: '/images/contact-og.jpg',
                width: 1200,
                height: 630,
                alt: 'Contact Detail On The Go'
            }
        ],
        type: 'website',
    },
    twitter: {
        card: 'summary_large_image',
        title: 'Contact Us | Detail On The Go',
        description: 'Get in touch with Detail On The Go for mobile detailing services or franchise opportunities.',
        images: ['/images/contact-og.jpg'],
    }
};

export default function Layout({ children }: { children: React.ReactNode }) {
    return (
        <>
            {/* Structured Data for SEO */}
            <script
                type="application/ld+json"
                dangerouslySetInnerHTML={{
                    __html: JSON.stringify({
                        "@context": "https://schema.org",
                        "@type": "ContactPage",
                        "name": "Contact Us",
                        "url": "https://detailongo.com/contact/",
                        "description": "Contact Detail On The Go for mobile auto detailing services, franchise inquiries, or customer support.",
                        "publisher": {
                            "@type": "Organization",
                            "name": "Detail On The Go",
                            "url": "https://detailongo.com/",
                            "image": "https://detailongo.com/images/contact-og.jpg",
                            "telephone": "+1-************"
                        }
                    })
                }}
            />
            {children}
        </>
    );
}
