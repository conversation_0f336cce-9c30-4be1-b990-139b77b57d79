export interface Branch {
    branch: string;
    businessNumber: string;
    calendarId: string;
    collectionId: string;
    location: string;
    link: string;
    introMp3: string;
    allowedEmails: string[];
    employeeName: string;
    employeeNumber: string;
    reviewLink: string;
    lat: number;
    lng: number;
}

// Helper type to extract location keys from your existing locationValues
export type LocationKey = keyof typeof import('./locationValues').locationValues;