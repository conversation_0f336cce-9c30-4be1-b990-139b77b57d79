"use client";

import React, { useState, useEffect, ChangeEvent } from "react";
import { <PERSON>, <PERSON>R<PERSON>, ArrowLeft } from 'lucide-react';

// Define FormData interface with industry as string array
interface FormData {
    contactFirstName: string;
    contactLastName: string;
    contactEmail: string;
    businessName: string;
    employeeCount: string;
    website: string;
    phone: string;
    address: string;
    industry: string[];
    howLongInBusiness: string;
    revenue: string;
}

// Define Errors interface
interface Errors {
    [key: string]: string;
}

// Define Question type with optional multiple property
interface Question {
    id: string;
    type: string;
    title: string;
    description: string;
    required?: boolean;
    field?: keyof FormData;
    placeholder?: string;
    options?: string[];
    betaNotice?: string;
    multiple?: boolean;
}

interface BookingSoftwareSignupPopupProps {
    onClose: () => void;
}

const BookingSoftwareSignupPopup: React.FC<BookingSoftwareSignupPopupProps> = ({ onClose }) => {
    // State management
    const [currentStep, setCurrentStep] = useState(0);
    const [animateIn, setAnimateIn] = useState(true);
    const [formData, setFormData] = useState<FormData>({
        contactFirstName: "",
        contactLastName: "",
        contactEmail: "",
        businessName: "",
        employeeCount: "",
        website: "",
        phone: "",
        address: "",
        industry: [],
        howLongInBusiness: "",
        revenue: ""
    });
    const [errors, setErrors] = useState<Errors>({});
    const [isSubmitting, setIsSubmitting] = useState(false);
    const [submitStatus, setSubmitStatus] = useState("");
    const [activeInputPosition, setActiveInputPosition] = useState<number | null>(null);
    const [submitStartTime, setSubmitStartTime] = useState<number>(0);
    const [loadingDots, setLoadingDots] = useState(0);
    const [isMobile, setIsMobile] = useState(false);

    const [buttonsPosition, setButtonsPosition] = useState<number>(0);


    useEffect(() => {
        const checkMobile = () => {
            setIsMobile(window.matchMedia("(hover: none)").matches);
        };
        checkMobile();
        window.addEventListener('resize', checkMobile);
        return () => window.removeEventListener('resize', checkMobile);
    }, []);

    const handleInputFocus = (e: React.FocusEvent<HTMLInputElement>) => {
        const input = e.target;
        setTimeout(() => {
            const inputRect = input.getBoundingClientRect();
            const viewportHeight = window.innerHeight;
            const keyboardHeight = viewportHeight * 0.4;
            const currentQuestionId = questions[currentStep].id;

            let positionOffset = 10;
            if (currentQuestionId === 'contactEmail' || currentQuestionId === 'phone') {
                positionOffset = -40;
            }

            const maxVisibleHeight = viewportHeight - keyboardHeight;
            const desiredPosition = inputRect.bottom + positionOffset;
            const safePosition = Math.min(desiredPosition, maxVisibleHeight - 150);

            setButtonsPosition(safePosition);

            // Fixed scroll options with proper typing
            input.scrollIntoView({
                behavior: 'smooth',
                block: currentQuestionId === 'contactEmail' ? 'start' : 'center' as ScrollLogicalPosition
            });
        }, 300);
    };



    const handleInputBlur = () => {
        setButtonsPosition(0); // Reset to bottom
    };


    // Define steps/questions
    const questions: Question[] = [
        {
            id: 'intro',
            type: 'intro',
            title: "Sign up For Early Access",
            description: "",
            betaNotice: "Our booking, scheduling, marketing & SEO software package is currently in beta testing phase. Signing up now puts you on our exclusive beta testers wait list. Not all applicants will be accepted during this initial phase as we're carefully scaling our platform.",
        },
        {
            id: 'contactFirstName',
            type: 'text',
            title: "What's your first name?",
            description: "",
            required: true,
            field: 'contactFirstName',
            placeholder: 'Type here...'
        },
        {
            id: 'contactLastName',
            type: 'text',
            title: "And your last?",
            description: "",
            required: true,
            field: 'contactLastName',
            placeholder: 'Type here...'
        },
        {
            id: 'contactEmail',
            type: 'email',
            title: "What's your email address?",
            description: "",
            required: true,
            field: 'contactEmail',
            placeholder: '<EMAIL>'
        },
        {
            id: 'phone',
            type: 'text',
            title: "Thanks {firstNAME}, What's your business phone number?",
            description: "If you don't have a business phone, just enter your personal number.",
            required: true,
            field: 'phone',
            placeholder: '************'
        },
        {
            id: 'businessName',
            type: 'text',
            title: "What's your businesses name?",
            description: "If you don't have one yet, just type 'N/A'.",
            required: true,
            field: 'businessName',
            placeholder: 'Type here...'
        },
        {
            id: 'employeeCount',
            type: 'text',
            title: "And how many employees does {Business name} have?",
            description: "",
            required: true,
            field: 'employeeCount',
            placeholder: 'Type here...'
        },
        {
            id: 'website',
            type: 'text',
            title: "What's your website?",
            description: "If you don't have one, just type 'N/A' and we can easily set one up for you.",
            required: true,
            field: 'website',
            placeholder: 'businessdomain.com'
        },
        {
            id: 'address',
            type: 'text',
            title: "Where is your business located?",
            description: "",
            required: true,
            field: 'address',
            placeholder: 'Enter a street address or city'
        },
        {
            id: 'industry',
            type: 'multiple-choice',
            title: "What industry are you in?",
            description: "Select all that apply.",
            required: true,
            field: 'industry',
            options: ["Car, Boat or RV Detailing", "Paintless Dent Removal", "Mechanic", "Window Tinting", "Auto Glass Repair and Replacement", "Other"],
            multiple: true
        },
        {
            id: 'howLongInBusiness',
            type: 'multiple-choice',
            title: "How long have you been in business?",
            description: "This helps us understand your experience level.",
            required: true,
            field: 'howLongInBusiness',
            options: ["Less than 1 year", "1-3 years", "3-5 years", "More than 5 years"]
        },
        {
            id: 'revenue',
            type: 'multiple-choice',
            title: "What is your annual revenue?",
            description: "",
            required: true,
            field: 'revenue',
            options: ["Under $50,000", "$50,000 - $100,000", "$100,000 - $500,000", "Over $500,000"]
        }
    ];

    // Email validation
    const validateEmail = (email: string) => {
        const re = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        return re.test(email);
    };

    // Validate the current question
    const validateCurrentQuestion = (): boolean => {
        const currentQ = questions[currentStep];
        if (!currentQ.required || !currentQ.field) return true;

        const field = currentQ.field;
        const fieldValue = formData[field];

        if (currentQ.multiple) {
            if (!Array.isArray(fieldValue) || fieldValue.length === 0) {
                setErrors({ [field]: 'Please select at least one option' });
                return false;
            }
        } else if (!fieldValue) {
            setErrors({ [field]: 'This field is required' });
            return false;
        } else if (currentQ.type === 'email' && !validateEmail(fieldValue as string)) {
            setErrors({ [field]: 'Please enter a valid email address' });
            return false;
        }

        setErrors({});
        return true;
    };

    // Handle proceeding to the next step
    const handleNext = (skipValidation = false) => {
        if (skipValidation || validateCurrentQuestion()) {
            if (currentStep === questions.length - 1) {
                handleSubmit();
            } else {
                setAnimateIn(false);
                setTimeout(() => {
                    setCurrentStep(prev => prev + 1);
                    setAnimateIn(true);
                }, 300);
            }
        }
    };

    // Handle going back to the previous step
    const handlePrevious = () => {
        if (currentStep > 0) {
            setAnimateIn(false);
            setTimeout(() => {
                setCurrentStep(prev => prev - 1);
                setAnimateIn(true);
            }, 300);
        }
    };

    // Handle text input changes
    const handleInputChange = (e: ChangeEvent<HTMLInputElement>) => {
        const { name, value } = e.target;
        setFormData(prev => ({ ...prev, [name as keyof FormData]: value }));
        if (errors[name]) {
            setErrors(prev => ({ ...prev, [name]: '' }));
        }
    };

    // Handle option selection for multiple-choice questions
    const handleOptionSelect = (field: keyof FormData, value: string, multiple: boolean = false) => {
        if (multiple) {
            setFormData(prev => {
                const currentValues = (prev[field] as string[]) || [];
                if (currentValues.includes(value)) {
                    return { ...prev, [field]: currentValues.filter(v => v !== value) };
                } else {
                    return { ...prev, [field]: [...currentValues, value] };
                }
            });
        } else {
            // single‐choice fields
            setFormData(prev => ({ ...prev, [field]: value }));

            // clear any existing error for this field
            if (errors[field]) {
                setErrors(prev => ({ ...prev, [field]: '' }));
            }

            // immediately advance for all single‐choice questions except revenue
            if (field !== 'revenue') {
                handleNext(true);
            }
        }
    };

    // 2) Add this useEffect below your other hooks to watch for revenue selection:
    useEffect(() => {
        // questions.length - 1 points to the 'revenue' step
        if (formData.revenue && currentStep === questions.length - 1) {
            // when revenue is set and we're on that step, auto-advance (which will call handleSubmit)
            handleNext(true);
        }
    }, [formData.revenue, currentStep]);

    // Handle form submission
    // Handle form submission
    const handleSubmit = async () => {
        const startTime = Date.now();
        setSubmitStartTime(startTime);

        // Log the current form data to debug
        console.log("Submitting form with data:", formData);

        // Validate all required fields before submission
        const requiredFields: (keyof FormData)[] = [
            'contactFirstName', 'contactLastName', 'contactEmail', 'businessName',
            'employeeCount', 'website', 'phone', 'address', 'howLongInBusiness', 'revenue'
        ];
        let formErrors: Errors = {};

        requiredFields.forEach(field => {
            const value = formData[field];
            if (field === 'revenue' && !value) {
                formErrors[field] = 'Please select your annual revenue';
            } else if (typeof value === 'string' && value.trim() === '') {
                formErrors[field] = 'This field is required';
            } else if (field === 'contactEmail' && !validateEmail(value as string)) {
                formErrors[field] = 'Please enter a valid email address';
            }
        });

        if (formData.industry.length === 0) {
            formErrors['industry'] = 'Please select at least one option';
        }

        if (Object.keys(formErrors).length > 0) {
            setErrors(formErrors);
            setSubmitStatus("Please fill all required fields.");
            return;
        }

        setIsSubmitting(true);
        setSubmitStatus("Loading");

        // Start loading animation
        const dotsInterval = setInterval(() => {
            setLoadingDots(prev => (prev + 1) % 4);
        }, 500);

        try {
            // Step 1: Add email to subscriber list
            const subscriberData = {
                email: formData.contactEmail,
                industries: formData.industry.join(", "), // Optional: Include industries as a string
            };

            const addSubscriberUrl = process.env.NEXT_PUBLIC_ADD_SUBSCRIBER || "https://newsletter-896343340170.us-central1.run.app";
            const subscriberResponse = await fetch(addSubscriberUrl, {
                method: "POST",
                headers: { "Content-Type": "application/json" },
                body: JSON.stringify(subscriberData),
            });

            if (!subscriberResponse.ok) {
                throw new Error("Failed to add email to subscriber list");
            }

            // Step 2: Send notification email (existing logic)
            const htmlMessage = `
            <h2>New Detail On The Go Beta Signup</h2>
            <p><strong>Contact First Name:</strong> ${formData.contactFirstName}</p>
            <p><strong>Contact Last Name:</strong> ${formData.contactLastName}</p>
            <p><strong>Contact Email:</strong> ${formData.contactEmail}</p>
            <p><strong>Business Name:</strong> ${formData.businessName}</p>
            <p><strong>Employee Count:</strong> ${formData.employeeCount}</p>
            <p><strong>Website:</strong> ${formData.website}</p>
            <p><strong>Phone:</strong> ${formData.phone}</p>
            <p><strong>Address:</strong> ${formData.address}</p>
            <p><strong>Industry:</strong> ${formData.industry.join(', ')}</p>
            <p><strong>How Long in Business:</strong> ${formData.howLongInBusiness}</p>
            <p><strong>Revenue:</strong> ${formData.revenue}</p>
        `;

            const payload = {
                fromName: `${formData.contactFirstName} ${formData.contactLastName}`,
                fromEmail: formData.contactEmail,
                to: '<EMAIL>',
                subject: `New Detail On The Go Beta Signup: ${formData.contactFirstName}`,
                message: htmlMessage,
            };

            const res = await fetch('/api/send-email', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify(payload),
            });

            if (res.ok) {
                // Send confirmation email to the visitor
                await fetch('/api/send-email', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        fromName: 'Detail On The Go',
                        fromEmail: '<EMAIL>',
                        to: formData.contactEmail,
                        subject: 'the perfect booking system for you',
                        message: `
                        <h2>Thanks for Applying to the Detail On The Go Beta</h2>
                        <p>We’re excited you’ve expressed interest in our next-generation booking system. As part of our selective early-access program, we carefully review each applicant to ensure the perfect fit.</p>
                        <p>Our team will vet your submission. If you’re chosen to join, you’ll receive a personalized onboarding link to explore features like dynamic pricing, mobile-first multistep booking, and centralized messaging.</p>
                        <p>Keep an eye on your inbox—and feel free to reply if you have any questions.</p>
                        <p>— The Detail On The Go Team</p>
                    `
                    }),
                });

                // Success handling
                console.log("Form submitted successfully");
                setIsSubmitting(false);
                setSubmitStatus("Submitted successfully! We will be in touch soon.");
                setTimeout(() => onClose(), 2000);
            } else {
                throw new Error("Failed to send notification email");
            }

            const elapsed = Date.now() - startTime;
            if (elapsed < 5000) {
                await new Promise(resolve => setTimeout(resolve, 5000 - elapsed));
            }

            setSubmitStatus("Submitted successfully! We will be in touch soon.");
            setTimeout(() => onClose(), 2000);
        } catch (err) {
            console.error("Submission Error:", err);
            setSubmitStatus("Error submitting form. Please try again.");
        } finally {
            clearInterval(dotsInterval);
            setIsSubmitting(false);
        }
    };

    // Add keyboard navigation
    useEffect(() => {
        const handleKeyDown = (e: KeyboardEvent) => {
            if (e.key === 'Escape') {
                onClose();
            } else if (e.key === 'Enter' && !e.shiftKey) {
                if (document.activeElement?.tagName === 'INPUT') {
                    e.preventDefault();
                    handleNext(false);
                }
            }
        };

        window.addEventListener('keydown', handleKeyDown);
        return () => window.removeEventListener('keydown', handleKeyDown);
    }, [currentStep, formData]);

    // Replace placeholders in titles and descriptions
    const replacePlaceholders = (text: string) => {
        return text
            .replace('{firstNAME}', formData.contactFirstName || '')
            .replace('{Business name}', formData.businessName || '');
    };

    // Render the current question
    const renderQuestion = () => {
        const question = questions[currentStep];
        if (!question) return null;

        const commonClasses = "transition-all duration-300 w-full max-w-xl";
        const animationClasses = animateIn
            ? "opacity-100 translate-y-0"
            : "opacity-0 translate-y-8";

        switch (question.type) {
            case 'intro':
                return (
                    <div className={`${commonClasses} ${animationClasses} text-center`}>
                        <h2 className="text-4xl font-bold mb-4">{replacePlaceholders(question.title)}</h2>
                        <p className="text-xl mb-8">{replacePlaceholders(question.description)}</p>
                        {question.betaNotice && (
                            <div className="bg-[#ff007f]/20 p-4 rounded-lg mb-8">
                                <p className="text-sm">{question.betaNotice}</p>
                            </div>
                        )}
                        <button
                            onClick={() => handleNext(false)}
                            className="bg-[#ff007f] hover:bg-[#d9006c] text-white font-bold py-3 px-8 rounded-lg flex items-center justify-center mx-auto"
                        >
                            Get Started <ArrowRight className="ml-2" size={20} />
                        </button>
                    </div>
                );

            case 'text':
            case 'email':
                return (
                    <div className={`${commonClasses} ${animationClasses}`}>
                        <h2 className="text-3xl font-bold mb-3">{replacePlaceholders(question.title)}</h2>
                        <p className="text-lg mb-6 text-gray-200">{replacePlaceholders(question.description)}</p>
                        <input
                            type={question.type}
                            name={question.field}
                            value={formData[question.field as keyof FormData] || ''}
                            onChange={handleInputChange}
                            onFocus={handleInputFocus}
                            onBlur={handleInputBlur}
                            placeholder={question.placeholder}
                            className="w-full bg-[#2318f6] border-b-2 border-white text-white text-xl py-3 px-4 focus:outline-none focus:border-[#ff007f] placeholder-gray-400"
                        />
                        {errors[question.field!] && (
                            <p className="text-[#ff007f] mt-2">{errors[question.field!]}</p>
                        )}
                    </div>
                );

            case 'multiple-choice':
                const isMultiple = question.multiple || false;
                const currentField = question.field as keyof FormData;

                // Debug output to check current values
                if (currentField === 'revenue') {
                    console.log("Current revenue value:", formData.revenue);
                }

                return (
                    <div className={`${commonClasses} ${animationClasses}`}>
                        <h2 className="text-3xl font-bold mb-3">{replacePlaceholders(question.title)}</h2>
                        <p className="text-lg mb-6 text-gray-200">{replacePlaceholders(question.description)}</p>
                        <div className="space-y-3">
                            {question.options?.map((option, idx) => {
                                const isSelected = isMultiple
                                    ? (formData[currentField] as string[])?.includes(option)
                                    : formData[currentField] === option;

                                return (
                                    <div
                                        key={idx}
                                        onClick={() => {
                                            handleOptionSelect(currentField, option, isMultiple);
                                            // Additional debug log
                                            console.log(`Clicked ${currentField} option:`, option);
                                        }}
                                        className={`p-4 rounded-lg border-2 cursor-pointer transition-all duration-150 ${isSelected
                                            ? 'border-[#ff007f] bg-[#ff007f]/20'
                                            : 'border-white/30 hover:border-white/70'
                                            }`}
                                    >
                                        <p className="text-lg">{option}</p>
                                    </div>
                                );
                            })}
                        </div>

                        {errors[currentField] && (
                            <p className="text-[#ff007f] mt-2">{errors[currentField]}</p>
                        )}
                    </div>
                );

            default:
                return null;
        }
    };

    const progress = ((currentStep) / (questions.length - 1)) * 100;

    return (
        <div className="fixed inset-0 z-50 flex flex-col bg-[#2318f6] text-white overflow-y-auto">
            {/* Close button and progress bar (unchanged) */}
            <button onClick={onClose} className="absolute top-4 right-4 p-2 rounded-full hover:bg-[#ff007f]/20 transition-colors">
                <X size={24} />
            </button>

            <div className="w-full h-1 bg-white/20">
                <div className="h-full bg-[#ff007f] transition-all duration-300" style={{ width: `${progress}%` }} />
            </div>

            {/* Main content area (now with bottom padding) */}
            <div className="flex-1 flex flex-col items-center justify-center p-6 pb-24"> {/* Added pb-24 */}
                {submitStatus ? (
                    <div className="transition-all duration-300 w-full max-w-xl opacity-100 translate-y-0 text-center">
                        <h2 className="text-4xl font-bold mb-4">
                            {submitStatus.includes("success") ? "Thank You!" :
                                submitStatus.includes("Error") ? "Oops!" : "Processing"}
                        </h2>
                        <p className={`text-xl mb-8 ${submitStatus.includes('success') ? 'text-green-400' :
                            submitStatus.includes('Error') ? 'text-[#ff007f]' : 'text-white'}`}>
                            {submitStatus}{submitStatus === "Loading" && '.'.repeat(loadingDots)}
                        </p>
                    </div>
                ) : (
                    renderQuestion()
                )}

            </div>

            {/* Fixed navigation buttons (now stays above keyboard) */}
            {!submitStatus && (
                <div
                    className="fixed left-0 right-0 bg-[#2318f6] p-6 flex justify-between border-t border-white/20 transition-all duration-300"
                    style={{
                        bottom: isMobile && buttonsPosition > 0 ? `${buttonsPosition}px` : '0px',
                        transform: 'translateY(0)',
                        visibility: 'visible'
                    }}
                >
                    <button
                        onClick={handlePrevious}
                        disabled={currentStep === 0}
                        className={`flex items-center ${currentStep === 0 ? 'invisible' : 'text-white hover:text-[#ff007f]'}`}
                    >
                        <ArrowLeft size={20} className="mr-2" /> Back
                    </button>

                    {currentStep > 0 && (
                        <button
                            onClick={() => {
                                setActiveInputPosition(null); // Reset position on navigation
                                handleNext(false);
                            }}
                            className="flex items-center text-white hover:text-[#ff007f]"
                        >
                            {currentStep === questions.length - 1 ? 'Submit' : 'Next'}
                            <ArrowRight size={20} className="ml-2" />
                        </button>
                    )}
                </div>
            )}
        </div>
    );
};

export default BookingSoftwareSignupPopup;