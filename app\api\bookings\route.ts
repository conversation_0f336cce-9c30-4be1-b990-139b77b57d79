import { NextResponse } from 'next/server';
import { db } from '@/lib/firebase/firebase';
import { collection, addDoc, serverTimestamp } from 'firebase/firestore';

interface Vehicle {
    type: string;
    size?: string;
    year?: string;
    make?: string;
    model?: string;
    package?: string;
    services?: string[];
    length?: number;
    addons: { name: string; quantity: number }[];
    basePrice: number;
    addonsPrice: number;
    totalPrice: number;
    pricingDetails?: { description: string; amount: number }[];
}

interface BookingData {
    firstName: string;
    lastName: string;
    email: string;
    phone: string;
    address: string;
    date: string;
    time: string;
    vehicles: Vehicle[];
    totalPrice: number;
    stripeAddress: {
        line1: string;
        city: string;
        state: string;
        postal_code: string;
        country: string;
    };
    notes?: string;
    termsAgreed: boolean;
    isRecurring?: boolean;
    frequency?: string;
    calculatedPrice: number;
    stripeName: string;
    stripeEmail: string;
    stripePhone: string;
    waterElectricity: string;
    garageParking: string;
    bathroomAvailable: string;
    howFound: string;
    branch?: string;
    paymentStatus: 'pending' | 'succeeded' | 'failed' | 'not_required';
    paymentError?: string;
    paymentDate?: string;
    bookingId?: string;
    reviewLink?: string; // Add review link to interface
}

const locationValues = {
    "+***********": {
        branch: "lwr",
        businessNumber: "+***********",
        calendarId: '<EMAIL>',
        collectionId: "sms-lwr",
        location: "Lawrence/KC and the surrounding areas",
        link: "https://www.detailongo.com/#lwrphone",
        introMp3: "https://blue-cat-8947.twil.io/assets/intro-lwr.mp3",
        allowedEmails: ["<EMAIL>", "<EMAIL>", "<EMAIL>"],
        employeeName: "Levi Taylor",
        employeeNumber: "+***********",
        reviewLink: "https://g.page/r/CS98X9jMS0IREBM/review",
        lat: 38.9717,
        lng: -95.2353,
        businessEmail: "<EMAIL>"
    },
    "+***********": {
        branch: "w-stl",
        businessNumber: "+***********",
        calendarId: '<EMAIL>',
        collectionId: "sms-w-stl",
        location: "St. Louis and the surrounding areas",
        link: "https://www.detailongo.com/#stlphone",
        introMp3: "https://blue-cat-8947.twil.io/assets/intro-stl.mp3",
        allowedEmails: ["<EMAIL>"],
        employeeName: "Taylor Woods",
        employeeNumber: "+***********",
        reviewLink: "https://g.page/r/CaNuJ0ypIXA7EBM/review",
        lat: 38.6270,
        lng: -90.1994,
        businessEmail: "<EMAIL>"
    },
    "+***********": {
        branch: "la",
        businessNumber: "+***********",
        calendarId: '<EMAIL>',
        collectionId: "sms-la",
        location: "Los Angeles and the surrounding areas",
        link: "https://www.detailongo.com/#laphone",
        introMp3: "https://blue-cat-8947.twil.io/assets/intro-la.mp3",
        allowedEmails: ["<EMAIL>"],
        employeeName: "Jenny Biz",
        employeeNumber: "+***********",
        reviewLink: "https://g.page/r/CY3Q12skg1wsEBM/review",
        lat: 34.0522,
        lng: -118.2437,
        businessEmail: "<EMAIL>"
    },
    "+***********": {
        branch: "dvr",
        businessNumber: "+***********",
        calendarId: '<EMAIL>',
        collectionId: "sms-dvr",
        location: "Denver and the surrounding areas",
        link: "https://www.detailongo.com/#dvrphone",
        introMp3: "https://blue-cat-8947.twil.io/assets/intro-dvr.mp3",
        allowedEmails: ["<EMAIL>"],
        employeeName: "Alexis Orona",
        employeeNumber: "+***********",
        reviewLink: "https://g.page/r/CY3Q12skg1wsEBM/review",
        lat: 39.7392,
        lng: -104.9903,
        businessEmail: "<EMAIL>"
    },
    "+***********": {
        branch: "ny",
        businessNumber: "+***********",
        calendarId: '<EMAIL>',
        collectionId: "sms-ny",
        location: "New York and the surrounding areas",
        link: "https://www.detailongo.com/#nyphone",
        introMp3: "https://blue-cat-8947.twil.io/assets/intro-ny.mp3",
        allowedEmails: ["<EMAIL>"],
        employeeName: "Levi Taylor",
        employeeNumber: "+***********",
        reviewLink: "https://g.page/r/CTVgBNWsI5ZKEBM/review",
        lat: 40.7128,
        lng: -74.0060,
        businessEmail: "<EMAIL>"
    },
};

// Helper function to get review link by branch
function getReviewLinkByBranch(branch: string): string | null {
    const locationEntry = Object.values(locationValues).find(location => location.branch === branch);
    return locationEntry?.reviewLink || null;
}

async function createPaymentLink(bookingData: BookingData, bookingId: string) {
    try {
        // Only create payment link if there's an amount to pay
        if (!bookingData.totalPrice || bookingData.totalPrice <= 0) {
            return null;
        }

        // Create a descriptive product name with actual vehicle details
        const vehicleDescriptions = bookingData.vehicles.map((v, index) => {
            if (v.type === 'car') {
                // Use actual year/make/model for cars
                const vehicleInfo = [v.year, v.make, v.model].filter(Boolean).join(' ');
                return vehicleInfo ? `${vehicleInfo}: ${v.package}` : `Car #${index + 1}: ${v.package}`;
            } else if (v.type === 'boat') {
                // Use length for boats if available
                const boatInfo = v.length ? `${v.length}ft Boat` : `Boat #${index + 1}`;
                return `${boatInfo}: ${v.services?.join(', ') || 'Service'}`;
            } else if (v.type === 'rv') {
                // Use length for RVs if available
                const rvInfo = v.length ? `${v.length}ft RV` : `RV #${index + 1}`;
                return `${rvInfo}: ${v.services?.join(', ') || 'Service'}`;
            } else {
                // Fallback for other vehicle types
                return `${v.type.toUpperCase()} #${index + 1}: ${v.services?.join(', ') || 'Service'}`;
            }
        }).join(' | ');

        const productName = vehicleDescriptions || 'Detail Service';
        const description = `${bookingData.date} at ${bookingData.time} - ${bookingData.address}`;

        // Get review link for this branch - ENSURE this is set properly
        const reviewLink = getReviewLinkByBranch(bookingData.branch || 'lwr');

        // Create payment link document
        const paymentLinkData = {
            bookingId,
            customerName: `${bookingData.firstName} ${bookingData.lastName}`,
            customerEmail: bookingData.email,
            customerPhone: bookingData.phone || null,
            amount: Number(bookingData.totalPrice),
            productName,
            description,
            branch: bookingData.branch || 'lwr',
            reviewLink, // CRITICAL: Add review link to payment data

            // Complete booking details
            bookingDetails: {
                firstName: bookingData.firstName,
                lastName: bookingData.lastName,
                email: bookingData.email,
                phone: bookingData.phone,
                address: bookingData.address,
                date: bookingData.date,
                time: bookingData.time,
                vehicles: bookingData.vehicles,
                totalPrice: bookingData.totalPrice,
                notes: bookingData.notes,
                waterElectricity: bookingData.waterElectricity,
                garageParking: bookingData.garageParking,
                bathroomAvailable: bookingData.bathroomAvailable,
                howFound: bookingData.howFound,
                isRecurring: bookingData.isRecurring,
                frequency: bookingData.frequency,
                calculatedPrice: bookingData.calculatedPrice,
                stripeAddress: bookingData.stripeAddress,
                stripeName: bookingData.stripeName,
                stripeEmail: bookingData.stripeEmail,
                stripePhone: bookingData.stripePhone,
                termsAgreed: bookingData.termsAgreed,
                reviewLink: reviewLink // ALSO add to booking details
            },

            // Pre-fill customer information for payment form
            prefillData: {
                name: `${bookingData.firstName} ${bookingData.lastName}`,
                email: bookingData.email,
                phone: bookingData.phone || null
            },

            // Payment status
            isPaid: false,
            isOpened: false,
            viewed: false,
            viewCount: 0,

            // Timestamps
            createdAt: serverTimestamp(),
            lastViewed: null,
            paidAt: null,
            expiresAt: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000), // 30 days from now

            // Additional tracking
            paymentMethod: null,
            stripePaymentIntentId: null,
        };

        // Debug log to verify review link is being set
        console.log('Creating payment link with review link:', reviewLink);
        console.log('Branch:', bookingData.branch);

        // Add to Firebase
        const docRef = await addDoc(collection(db, 'paymentLinks'), paymentLinkData);

        // Generate payment URL
        const baseUrl = process.env.NEXT_PUBLIC_BASE_URL || 'https://detailongo.com';
        const paymentUrl = `${baseUrl}/pay/${docRef.id}`;

        return paymentUrl;

    } catch (error) {
        console.error('Error creating payment link:', error);
        return null;
    }
}

async function fetchWithRetry(url: string, options: RequestInit, retries = 3): Promise<Response> {
    try {
        const response = await fetch(url, options);
        if (!response.ok) throw new Error(`HTTP error! status: ${response.status}`);
        return response;
    } catch (error) {
        if (retries <= 0) throw error;
        const delay = 1000 * (4 - retries);
        console.log(`Retrying ${url} in ${delay}ms...`);
        await new Promise(resolve => setTimeout(resolve, delay));
        return fetchWithRetry(url, options, retries - 1);
    }
}

export async function POST(request: Request) {
    console.log('Received POST request to /api/bookings');
    try {
        const bookingData: BookingData = await request.json();
        console.log('Parsed bookingData:', JSON.stringify(bookingData, null, 2));

        // Validate core booking fields
        if (!bookingData.email || !bookingData.vehicles?.length) {
            return NextResponse.json(
                { error: "Missing required fields: email and vehicles are required" },
                { status: 400 }
            );
        }

        // Validate vehicle data
        for (const vehicle of bookingData.vehicles) {
            if (!vehicle.type || !vehicle.totalPrice) {
                return NextResponse.json(
                    { error: "Each vehicle must have a type and total price." },
                    { status: 400 }
                );
            }

            if (vehicle.type === 'car') {
                const missingFields = [];
                if (!vehicle.size) missingFields.push('size');
                if (!vehicle.package) missingFields.push('package');
                if (!vehicle.year) missingFields.push('year');
                if (!vehicle.make) missingFields.push('make');
                if (!vehicle.model) missingFields.push('model');

                if (missingFields.length > 0) {
                    return NextResponse.json(
                        { error: `Car vehicles require: ${missingFields.join(', ')}` },
                        { status: 400 }
                    );
                }
            }

            if ((vehicle.type === 'boat' || vehicle.type === 'rv') && !vehicle.services?.length) {
                return NextResponse.json(
                    { error: "Boat and RV vehicles must include at least one service." },
                    { status: 400 }
                );
            }
        }

        // Generate booking ID
        const bookingId = crypto.randomUUID();

        // Get review link for the branch - CRITICAL STEP
        const reviewLink = getReviewLinkByBranch(bookingData.branch || 'lwr');
        console.log('Review link for branch', bookingData.branch, ':', reviewLink);

        // Create payment link BEFORE sending to cloud functions
        let paymentUrl = null;
        if (bookingData.totalPrice > 0) {
            paymentUrl = await createPaymentLink(bookingData, bookingId);
            console.log('Payment link created:', paymentUrl);
        }

        const fullBookingData = {
            ...bookingData,
            bookingId,
            reviewLink, // CRITICAL: Add review link to booking data
            paymentStatus: bookingData.paymentStatus || 'pending',
            createdAt: new Date().toISOString(),
            paymentUrl // Add payment URL to booking data for cloud functions
        };

        // Save booking to database with review link
        try {
            await addDoc(collection(db, 'bookings'), {
                ...fullBookingData,
                createdAt: serverTimestamp()
            });
            console.log('Booking saved to Firebase with review link:', reviewLink);
        } catch (error) {
            console.error('Error saving booking to Firebase:', error);
            // Continue processing even if Firebase save fails
        }

        // Send to notification service (fire-and-forget) - now includes paymentUrl and reviewLink
        const cloudFunctionUrl = process.env.CLOUD_FUNCTION_URL;
        if (cloudFunctionUrl) {
            console.log('Sending booking data to notification cloud function...');
            fetchWithRetry(cloudFunctionUrl, {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify(fullBookingData),
            }).catch(error => {
                console.log('Notification cloud function error after retries:', error);
            });
        }

        // Send to calendar service (critical path) - now includes paymentUrl and reviewLink
        const calendarCloudFunctionUrl = "https://booking-reserve-896343340170.us-central1.run.app";
        console.log('Sending booking data to calendar/sheets cloud function...');
        try {
            const calendarResponse = await fetchWithRetry(calendarCloudFunctionUrl, {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify(fullBookingData),
            });
            console.log('Calendar cloud function response:', await calendarResponse.text());
        } catch (error) {
            const errorMessage = error instanceof Error ? error.message : 'Calendar sync failed';
            console.log('Calendar cloud function error after retries:', errorMessage);
            return NextResponse.json(
                { error: "Booking created but calendar sync failed", details: errorMessage },
                { status: 207 } // 207 Multi-Status
            );
        }

        return NextResponse.json({
            success: true,
            bookingId,
            reviewLink, // CRITICAL: Return review link to frontend
            paymentStatus: fullBookingData.paymentStatus,
            paymentUrl // Return payment URL to frontend if needed
        });

    } catch (error) {
        const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';
        console.error('Error in POST handler:', errorMessage);
        return NextResponse.json(
            { error: "Internal server error", details: errorMessage },
            { status: 500 }
        );
    }
}

// PATCH handler remains unchanged
export async function PATCH(request: Request) {
    try {
        const updateData: Partial<BookingData> = await request.json();

        if (!updateData.bookingId) {
            return NextResponse.json(
                { error: "Missing bookingId" },
                { status: 400 }
            );
        }

        // Update booking in database (implement your actual database logic here)
        // await updateBookingInDatabase(updateData.bookingId, updateData);

        return NextResponse.json({
            success: true,
            bookingId: updateData.bookingId
        });

    } catch (error) {
        const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';
        console.error('Error in PATCH handler:', errorMessage);
        return NextResponse.json(
            { error: "Failed to update booking", details: errorMessage },
            { status: 500 }
        );
    }
}