// app/api/process-payment/route.ts
import { NextResponse } from 'next/server';
import Stripe from 'stripe';

export async function POST(request: Request) {
    // 0. Initialize Stripe inside the handler so any init errors get caught
    let stripe: Stripe;
    try {
        stripe = new Stripe(process.env.STRIPE_SECRET_KEY!, {});
    } catch (initError: any) {
        console.error("Stripe initialization failed:", initError);
        return NextResponse.json(
            { error: initError.message || "Stripe initialization error" },
            { status: 500 }
        );
    }

    // 1. Parse and validate incoming JSON
    let paymentMethodId: string;
    let customerDetails: {
        stripeName: string;
        stripeEmail: string;
        stripePhone: string;
        stripeAddress: Stripe.AddressParam;
    };
    try {
        const body = await request.json();
        paymentMethodId = body.paymentMethodId;
        customerDetails = body.customerDetails;
        if (
            !paymentMethodId ||
            !customerDetails?.stripeName ||
            !customerDetails?.stripeEmail
        ) {
            throw new Error("Missing required payment or customer fields");
        }
    } catch (parseError: any) {
        console.error("Request parsing failed:", parseError);
        return NextResponse.json(
            { error: parseError.message || "Invalid JSON payload" },
            { status: 400 }
        );
    }

    // 2. Perform Stripe operations
    try {
        // 2a. Create customer
        const customer = await stripe.customers.create({
            name: customerDetails.stripeName,
            email: customerDetails.stripeEmail,
            phone: customerDetails.stripePhone,
            address: customerDetails.stripeAddress,
        });

        // 2b. Attach payment method
        await stripe.paymentMethods.attach(paymentMethodId, {
            customer: customer.id,
        });

        // 2c. Create and confirm a PaymentIntent
        const paymentIntent = await stripe.paymentIntents.create({
            amount: 50, // $0.50 in cents (adjust as needed!)
            currency: "usd",
            customer: customer.id,
            payment_method: paymentMethodId,
            confirm: true,
            setup_future_usage: "off_session",
            automatic_payment_methods: {
                enabled: true,
                allow_redirects: "never",
            },
            metadata: {
                initial_charge: "true",
                booking_id: "your_booking_reference",
            },
        });

        // 3. Return JSON success payload
        return NextResponse.json({
            success: true,
            clientSecret: paymentIntent.client_secret,
            customerId: customer.id,
            requiresAction: paymentIntent.status === "requires_action",
        });
    } catch (stripeError: any) {
        console.error("Payment processing error:", stripeError);
        // Always return JSON—even on Stripe errors
        return NextResponse.json(
            { error: stripeError.message || "Payment processing failed" },
            { status: 500 }
        );
    }
}
