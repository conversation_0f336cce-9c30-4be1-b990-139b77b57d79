import { NextResponse } from 'next/server';

// Vehicle interface (copied from /api/bookings for consistency)
interface Vehicle {
    type: string;
    size?: string;
    year?: string;
    make?: string;
    model?: string;
    package?: string;
    services?: string[];
    length?: number;
    addons: { name: string; quantity: number }[];
    basePrice: number;
    addonsPrice: number;
    totalPrice: number;
    pricingDetails?: { description: string; amount: number }[];
}

// BookingData interface (copied from /api/bookings)
interface BookingData {
    firstName: string;
    lastName: string;
    email: string;
    phone: string;
    address: string;
    date: string;
    time: string;
    vehicles: Vehicle[];
    totalPrice: number;
    stripeCustomerId: string;
    stripeAddress: {
        line1: string;
        city: string;
        state: string;
        postal_code: string;
        country: string;
    };
    notes?: string;
    termsAgreed: boolean;
    isRecurring?: boolean;
    frequency?: string;
    calculatedPrice: number;
    stripeName: string;
    stripeEmail: string;
    stripePhone: string;
    waterElectricity: string;
    garageParking: string;
    bathroomAvailable: string;
    howFound: string;
    branch?: string;
}

// CancellationData interface extends BookingData partially and adds cancellation-specific fields
interface CancellationData extends Partial<BookingData> {
    currentStep?: number;
    cancelTimestamp?: string;
}

export async function POST(request: Request) {
    try {
        // Parse the incoming cancellation data
        const cancellationData: CancellationData = await request.json();
        console.log('Received cancellation data:', JSON.stringify(cancellationData, null, 2));

        // Get the test webhook URL from environment variables
        const testWebhookUrl = "https://cancel-booking-896343340170.us-central1.run.app";
        if (testWebhookUrl) {
            // Send the cancellation data to the test webhook
            const response = await fetch(testWebhookUrl, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(cancellationData),
            });

            if (!response.ok) {
                console.error('Failed to send data to test webhook:', await response.text());
            } else {
                console.log('Successfully sent data to test webhook');
            }
        } else {
            console.warn('Test webhook URL not configured. Skipping webhook notification.');
        }

        // Return success to the client regardless of webhook outcome
        return NextResponse.json({ success: true });
    } catch (error) {
        // Log any errors but still return success to the client
        console.error('Error in /api/cancel:', error);
        return NextResponse.json({ success: true }, { status: 200 });
    }
}