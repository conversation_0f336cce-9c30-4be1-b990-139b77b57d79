import { NextRequest, NextResponse } from 'next/server';
import <PERSON><PERSON> from 'stripe';

const stripe = new Stripe(process.env.STRIPE_SECRET_KEY!, {
});

export async function POST(request: NextRequest) {
    try {
        const { paymentMethodId } = await request.json();

        if (!paymentMethodId) {
            return NextResponse.json(
                { error: 'Payment method ID is required' },
                { status: 400 }
            );
        }

        const paymentMethod = await stripe.paymentMethods.retrieve(paymentMethodId);

        return NextResponse.json({
            card: {
                brand: paymentMethod.card?.brand,
                last4: paymentMethod.card?.last4,
            }
        });
    } catch (error) {
        console.error('Error retrieving payment method:', error);
        return NextResponse.json(
            { error: 'Failed to retrieve payment method' },
            { status: 500 }
        );
    }
}