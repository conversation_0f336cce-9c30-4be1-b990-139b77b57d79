import { MetadataRoute } from "next";
import { locationValues } from "../components/locationValues";

export default async function sitemap(): Promise<MetadataRoute.Sitemap> {
  const baseUrl = "https://detailongo.com";
  const locations = Object.keys(locationValues).map((phone) => ({
    url: `${baseUrl}/${phone}`,
    lastModified: new Date(),
  }));

  return [
    {
      url: baseUrl,
      lastModified: new Date(),
    },
    ...locations
  ];
}