// app/api/tax-calculation/route.ts
import { NextRequest, NextResponse } from 'next/server';
import <PERSON><PERSON> from 'stripe';

export async function POST(req: NextRequest) {
    // Get the request body
    const { amount, address } = await req.json();

    // Initialize Stripe with your secret key - add type assertion
    const stripeSecretKey = process.env.STRIPE_LIVE_TAX_KEY;

    if (!stripeSecretKey) {
        return NextResponse.json(
            { success: false, error: 'Stripe API key is not configured' },
            { status: 500 }
        );
    }

    const stripe = new Stripe(stripeSecretKey, {
    });

    try {
        // Call Stripe's tax calculation API
        const calculation = await stripe.tax.calculations.create({
            currency: 'usd',
            customer_details: {
                address: address,
                address_source: 'shipping',
            },
            line_items: [
                {
                    amount: amount,
                    reference: 'service_charge',
                    tax_behavior: 'exclusive',
                },
            ],
        });

        // Get the actual tax rate from <PERSON>e's breakdown
        const taxBreakdown = calculation.tax_breakdown?.[0];
        console.log('Full tax breakdown:', JSON.stringify(taxBreakdown, null, 2));

        const actualTaxRate = taxBreakdown ? (taxBreakdown.tax_rate_details?.percentage_decimal || 0) : 0;

        // Convert to number and then to percentage if needed
        const numericTaxRate = typeof actualTaxRate === 'string' ? parseFloat(actualTaxRate) : actualTaxRate;
        const taxPercentage = numericTaxRate > 1 ? numericTaxRate : numericTaxRate * 100;

        console.log('Tax calculation debug:', {
            taxAmount: calculation.tax_amount_exclusive,
            rawTaxRate: actualTaxRate,
            convertedTaxRate: taxPercentage,
            taxBreakdown: calculation.tax_breakdown
        });

        // Return the calculation results
        return NextResponse.json({
            success: true,
            taxAmount: calculation.tax_amount_exclusive,
            totalAmount: amount + calculation.tax_amount_exclusive,
            taxPercentage: taxPercentage,
        });
    } catch (error: unknown) {
        console.error('Tax calculation failed:', error);

        // Handle the error safely with type checking
        let errorMessage = 'Unknown error occurred';
        if (error instanceof Error) {
            errorMessage = error.message;
        } else if (typeof error === 'string') {
            errorMessage = error;
        }

        return NextResponse.json(
            {
                success: false,
                error: errorMessage
            },
            { status: 500 }
        );
    }
}
