import { NextRequest, NextResponse } from "next/server";
import nodemailer from "nodemailer";

// Interface for the cleaning service form data
interface CleaningServiceFormData {
  firstName: string;
  lastName: string;
  email: string;
  phone: string;
  streetAddress: string;
  apartment: string;
  city: string;
  state: string;
  zipCode: string;
  billingDifferent: boolean;
  billingStreetAddress: string;
  billingApartment: string;
  billingCity: string;
  billingState: string;
  billingZipCode: string;
  propertyType: string;
  bedrooms: string;
  bathrooms: string;
  howHeard: string;
  roomsToSkip: string;
  allergies: string;
  cleaningPriorities: string;
  additionalServices: string[];
  frequency: string;
  budget: string;
  agreedToTerms: boolean;
}

export async function POST(req: NextRequest) {
  try {
    const formData: CleaningServiceFormData = await req.json();

    // Create transporter
    const transporter = nodemailer.createTransporter({
      host: process.env.SMTP_HOST,
      port: Number(process.env.SMTP_PORT),
      secure: Number(process.env.SMTP_PORT) === 465,
      auth: {
        user: process.env.SMTP_USER!,
        pass: process.env.SMTP_PASS!,
      },
    });

    // Generate email content
    const customerEmailContent = generateCustomerEmailContent(formData);
    const businessEmailContent = generateBusinessEmailContent(formData);
    const customerTextContent = generateCustomerTextContent(formData);
    const businessTextContent = generateBusinessTextContent(formData);

    // Send email to customer
    const customerMailOptions = {
      from: process.env.SMTP_USER,
      to: formData.email,
      subject: "Thank you for your cleaning service request - Peak Services AK",
      text: customerTextContent,
      html: customerEmailContent,
    };

    // Send email to business owner
    const businessMailOptions = {
      from: process.env.SMTP_USER,
      to: "<EMAIL>",
      subject: `New Cleaning Service Request from ${formData.firstName} ${formData.lastName}`,
      text: businessTextContent,
      html: businessEmailContent,
    };

    // Send both emails
    await Promise.all([
      transporter.sendMail(customerMailOptions),
      transporter.sendMail(businessMailOptions)
    ]);

    return NextResponse.json({ 
      success: true, 
      message: "Emails sent successfully" 
    });

  } catch (error: any) {
    console.error("Email sending error:", error);
    return NextResponse.json(
      { error: error.message },
      { status: 500 }
    );
  }
}

function generateCustomerEmailContent(data: CleaningServiceFormData): string {
  return `
    <!DOCTYPE html>
    <html>
      <head>
        <style>
          body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
          .container { max-width: 600px; margin: 0 auto; padding: 20px; }
          .header { background: linear-gradient(to right, #003cffff, #1e92f8); color: white; padding: 20px; text-align: center; border-radius: 8px 8px 0 0; }
          .content { background: #f9f9f9; padding: 20px; border-radius: 0 0 8px 8px; }
          h1 { margin: 0; font-size: 24px; }
          h2 { color: #003cffff; margin-top: 20px; border-bottom: 2px solid #1e92f8; padding-bottom: 5px; }
          .highlight { background: #e8f4fd; padding: 15px; border-radius: 5px; margin: 15px 0; }
          .footer { margin-top: 30px; font-size: 12px; color: #666; text-align: center; }
        </style>
      </head>
      <body>
        <div class="container">
          <div class="header">
            <h1>Thank You for Your Request!</h1>
            <p>Peak Services AK - Professional Cleaning Services</p>
          </div>
          
          <div class="content">
            <p>Dear ${data.firstName},</p>
            
            <p>Thank you for submitting your cleaning service request! We've received your information and will contact you within 24 hours to discuss your needs and provide a free estimate.</p>
            
            <div class="highlight">
              <h3>What happens next?</h3>
              <ul>
                <li>We'll review your request and contact you within 24 hours</li>
                <li>We'll discuss your specific cleaning needs and preferences</li>
                <li>We'll provide you with a free, detailed estimate</li>
                <li>We'll schedule your cleaning service at your convenience</li>
              </ul>
            </div>
            
            <h2>Your Request Summary</h2>
            <p><strong>Contact:</strong> ${data.firstName} ${data.lastName}</p>
            <p><strong>Phone:</strong> ${data.phone}</p>
            <p><strong>Property Address:</strong> ${data.streetAddress}${data.apartment ? `, ${data.apartment}` : ''}, ${data.city}, ${data.state} ${data.zipCode}</p>
            ${data.propertyType ? `<p><strong>Property Type:</strong> ${data.propertyType}</p>` : ''}
            ${data.frequency ? `<p><strong>Service Frequency:</strong> ${data.frequency}</p>` : ''}
            
            <div class="highlight">
              <p><strong>Questions?</strong> Feel free to call us or reply to this email. We're here to help!</p>
            </div>
            
            <p>Thank you for choosing Peak Services AK!</p>
            <p>The Peak Services Team</p>
          </div>
          
          <div class="footer">
            Peak Services AK - Professional Cleaning Services in Ketchikan, Alaska<br>
            This email was sent in response to your cleaning service request.
          </div>
        </div>
      </body>
    </html>
  `;
}

function generateBusinessEmailContent(data: CleaningServiceFormData): string {
  const propertyAddress = `${data.streetAddress}${data.apartment ? `, ${data.apartment}` : ''}, ${data.city}, ${data.state} ${data.zipCode}`;
  const billingAddress = data.billingDifferent ? 
    `${data.billingStreetAddress}${data.billingApartment ? `, ${data.billingApartment}` : ''}, ${data.billingCity}, ${data.billingState} ${data.billingZipCode}` : 
    'Same as property address';

  return `
    <!DOCTYPE html>
    <html>
      <head>
        <style>
          body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
          .container { max-width: 700px; margin: 0 auto; padding: 20px; }
          .header { background: linear-gradient(to right, #003cffff, #1e92f8); color: white; padding: 20px; text-align: center; border-radius: 8px 8px 0 0; }
          .content { background: #f9f9f9; padding: 20px; border-radius: 0 0 8px 8px; }
          h1 { margin: 0; font-size: 24px; }
          h2 { color: #003cffff; margin-top: 20px; border-bottom: 2px solid #1e92f8; padding-bottom: 5px; }
          .field { margin-bottom: 15px; }
          .label { font-weight: bold; color: #003cffff; }
          .value { margin-top: 5px; }
          .urgent { background: #fff3cd; border: 1px solid #ffeaa7; padding: 15px; border-radius: 5px; margin: 15px 0; }
          .services-list { background: white; padding: 15px; border-radius: 5px; margin: 10px 0; }
          .footer { margin-top: 30px; font-size: 12px; color: #666; text-align: center; }
        </style>
      </head>
      <body>
        <div class="container">
          <div class="header">
            <h1>🆕 New Cleaning Service Request</h1>
            <p>Peak Services AK - Customer Request</p>
          </div>
          
          <div class="content">
            <div class="urgent">
              <strong>⏰ Action Required:</strong> Contact customer within 24 hours to provide free estimate
            </div>
            
            <h2>📞 Contact Information</h2>
            <div class="field">
              <div class="label">Customer Name:</div>
              <div class="value">${data.firstName} ${data.lastName}</div>
            </div>
            <div class="field">
              <div class="label">Email:</div>
              <div class="value">${data.email}</div>
            </div>
            <div class="field">
              <div class="label">Phone:</div>
              <div class="value">${data.phone}</div>
            </div>
            
            <h2>🏠 Property Information</h2>
            <div class="field">
              <div class="label">Property Address:</div>
              <div class="value">${propertyAddress}</div>
            </div>
            <div class="field">
              <div class="label">Billing Address:</div>
              <div class="value">${billingAddress}</div>
            </div>
            ${data.propertyType ? `
            <div class="field">
              <div class="label">Property Type:</div>
              <div class="value">${data.propertyType}</div>
            </div>` : ''}
            ${data.bedrooms ? `
            <div class="field">
              <div class="label">Bedrooms:</div>
              <div class="value">${data.bedrooms}</div>
            </div>` : ''}
            ${data.bathrooms ? `
            <div class="field">
              <div class="label">Bathrooms:</div>
              <div class="value">${data.bathrooms}</div>
            </div>` : ''}
            
            <h2>🧹 Service Details</h2>
            ${data.frequency ? `
            <div class="field">
              <div class="label">Service Frequency:</div>
              <div class="value">${data.frequency}</div>
            </div>` : ''}
            ${data.cleaningPriorities ? `
            <div class="field">
              <div class="label">Cleaning Priorities:</div>
              <div class="value">${data.cleaningPriorities}</div>
            </div>` : ''}
            ${data.additionalServices.length > 0 ? `
            <div class="field">
              <div class="label">Additional Services Requested:</div>
              <div class="services-list">
                <ul>
                  ${data.additionalServices.map(service => `<li>${service}</li>`).join('')}
                </ul>
              </div>
            </div>` : ''}
            ${data.budget ? `
            <div class="field">
              <div class="label">Budget Considerations:</div>
              <div class="value">${data.budget}</div>
            </div>` : ''}
            
            <h2>⚠️ Special Requirements</h2>
            ${data.roomsToSkip ? `
            <div class="field">
              <div class="label">Rooms to Skip:</div>
              <div class="value">${data.roomsToSkip}</div>
            </div>` : ''}
            ${data.allergies ? `
            <div class="field">
              <div class="label">Allergies/Sensitivities:</div>
              <div class="value">${data.allergies}</div>
            </div>` : ''}
            
            <h2>📊 Additional Information</h2>
            ${data.howHeard ? `
            <div class="field">
              <div class="label">How they heard about us:</div>
              <div class="value">${data.howHeard}</div>
            </div>` : ''}
            <div class="field">
              <div class="label">Request submitted:</div>
              <div class="value">${new Date().toLocaleString()}</div>
            </div>
            <div class="field">
              <div class="label">Terms agreed:</div>
              <div class="value">${data.agreedToTerms ? 'Yes' : 'No'}</div>
            </div>
          </div>
          
          <div class="footer">
            Peak Services AK - New Customer Request Notification<br>
            Remember: Contact within 24 hours for best customer experience!
          </div>
        </div>
      </body>
    </html>
  `;
}

function generateCustomerTextContent(data: CleaningServiceFormData): string {
  return `
Thank You for Your Request!
Peak Services AK - Professional Cleaning Services

Dear ${data.firstName},

Thank you for submitting your cleaning service request! We've received your information and will contact you within 24 hours to discuss your needs and provide a free estimate.

WHAT HAPPENS NEXT?
- We'll review your request and contact you within 24 hours
- We'll discuss your specific cleaning needs and preferences
- We'll provide you with a free, detailed estimate
- We'll schedule your cleaning service at your convenience

YOUR REQUEST SUMMARY
Contact: ${data.firstName} ${data.lastName}
Phone: ${data.phone}
Property Address: ${data.streetAddress}${data.apartment ? `, ${data.apartment}` : ''}, ${data.city}, ${data.state} ${data.zipCode}
${data.propertyType ? `Property Type: ${data.propertyType}` : ''}
${data.frequency ? `Service Frequency: ${data.frequency}` : ''}

Questions? Feel free to call us or reply to this email. We're here to help!

Thank you for choosing Peak Services AK!
The Peak Services Team

Peak Services AK - Professional Cleaning Services in Ketchikan, Alaska
This email was sent in response to your cleaning service request.
  `;
}

function generateBusinessTextContent(data: CleaningServiceFormData): string {
  const propertyAddress = `${data.streetAddress}${data.apartment ? `, ${data.apartment}` : ''}, ${data.city}, ${data.state} ${data.zipCode}`;
  const billingAddress = data.billingDifferent ?
    `${data.billingStreetAddress}${data.billingApartment ? `, ${data.billingApartment}` : ''}, ${data.billingCity}, ${data.billingState} ${data.billingZipCode}` :
    'Same as property address';

  return `
NEW CLEANING SERVICE REQUEST
Peak Services AK - Customer Request

⏰ ACTION REQUIRED: Contact customer within 24 hours to provide free estimate

CONTACT INFORMATION
------------------
Customer Name: ${data.firstName} ${data.lastName}
Email: ${data.email}
Phone: ${data.phone}

PROPERTY INFORMATION
------------------
Property Address: ${propertyAddress}
Billing Address: ${billingAddress}
${data.propertyType ? `Property Type: ${data.propertyType}` : ''}
${data.bedrooms ? `Bedrooms: ${data.bedrooms}` : ''}
${data.bathrooms ? `Bathrooms: ${data.bathrooms}` : ''}

SERVICE DETAILS
------------------
${data.frequency ? `Service Frequency: ${data.frequency}` : ''}
${data.cleaningPriorities ? `Cleaning Priorities: ${data.cleaningPriorities}` : ''}
${data.additionalServices.length > 0 ? `Additional Services: ${data.additionalServices.join(', ')}` : ''}
${data.budget ? `Budget Considerations: ${data.budget}` : ''}

SPECIAL REQUIREMENTS
------------------
${data.roomsToSkip ? `Rooms to Skip: ${data.roomsToSkip}` : ''}
${data.allergies ? `Allergies/Sensitivities: ${data.allergies}` : ''}

ADDITIONAL INFORMATION
------------------
${data.howHeard ? `How they heard about us: ${data.howHeard}` : ''}
Request submitted: ${new Date().toLocaleString()}
Terms agreed: ${data.agreedToTerms ? 'Yes' : 'No'}

Peak Services AK - New Customer Request Notification
Remember: Contact within 24 hours for best customer experience!
  `;
}

export async function OPTIONS() {
  return NextResponse.json({}, {
    headers: {
      "Access-Control-Allow-Origin": "*",
      "Access-Control-Allow-Methods": "POST, OPTIONS",
      "Access-Control-Allow-Headers": "Content-Type",
    },
  });
}
