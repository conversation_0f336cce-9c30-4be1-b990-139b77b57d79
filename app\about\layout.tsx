import { Metadata } from 'next';

export const metadata: Metadata = {
    title: 'About Us | Peak Services AK - Professional Cleaning Services',
    description: 'Learn about Peak Services AK, founded by <PERSON> in 2022. We provide reliable, professional cleaning services throughout Ketchikan Alaska and all of S.E.',
    keywords: [
        'about Peak Services',
        '<PERSON>',
        'Alaska cleaning services',
        'Ketchikan cleaning company',
        'professional cleaning team',
        'local cleaning business',
        'residential cleaning',
        'commercial cleaning'
    ],
    alternates: {
        canonical: 'https://peakservicesak.com/about/',
    },
    openGraph: {
        title: 'About Us | Peak Services AK - Professional Cleaning Services',
        description: 'Learn about Peak Services AK, founded by <PERSON> in 2022. We provide reliable, professional cleaning services throughout Ketchikan Alaska and all of S.E.',
        url: 'https://peakservicesak.com/about/',
        images: [
            {
                url: '/megan owner of peak services.png',
                width: 1200,
                height: 630,
                alt: '<PERSON> - Owner of Peak Services AK'
            }
        ],
        type: 'website',
    },
    twitter: {
        card: 'summary_large_image',
        title: 'About Us | Peak Services AK - Professional Cleaning Services',
        description: 'Learn about Peak Services AK, founded by <PERSON> in 2022. We provide reliable, professional cleaning services throughout Ketchikan Alaska and all of S.E.',
        images: ['/megan owner of peak services.png'],
    }
};

export default function AboutLayout({
    children,
}: {
    children: React.ReactNode;
}) {
    return children;
}
