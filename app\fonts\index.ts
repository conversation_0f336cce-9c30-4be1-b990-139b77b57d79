import localFont from "next/font/local";

export const helvetica = localFont({
  src: [
    {
      path: "./HelvThin.otf",
      weight: "100",
      style: "normal",
    },
    {
      path: "./HelvThinItalic.otf",
      weight: "100",
      style: "italic",
    },
    {
      path: "./HelvUltraLight.otf",
      weight: "200",
      style: "normal",
    },
    {
      path: "./HelvUltraLightItalic.otf",
      weight: "200",
      style: "italic",
    },
    {
      path: "./HelvLight.otf",
      weight: "300",
      style: "normal",
    },
    {
      path: "./HelvLightItalic.otf",
      weight: "300",
      style: "italic",
    },
    {
      path: "./HelvRoman.otf",
      weight: "400",
      style: "normal",
    },
    {
      path: "./Helv.ttf",
      weight: "400",
      style: "normal",
    },
    {
      path: "./HelvMedium.otf",
      weight: "500",
      style: "normal",
    },
    {
      path: "./HelvMediumItalic.otf",
      weight: "500",
      style: "italic",
    },
    {
      path: "./HelvBold.otf",
      weight: "700",
      style: "normal",
    },
    {
      path: "./HelvBoldItalic.otf",
      weight: "700",
      style: "italic",
    },
    {
      path: "./HelvHeavy.otf",
      weight: "800",
      style: "normal",
    },
    {
      path: "./HelvHeavyItalic.otf",
      weight: "800",
      style: "italic",
    },
    {
      path: "./HelvBlack.otf",
      weight: "900",
      style: "normal",
    },
    {
      path: "./HelvBlackItalic.otf",
      weight: "900",
      style: "italic",
    },
  ],
  variable: "--font-helvetica",
});
