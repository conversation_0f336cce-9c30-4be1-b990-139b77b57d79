
import { NextRequest, NextResponse } from 'next/server';
import Stripe from 'stripe';

const stripe = new Stripe(process.env.STRIPE_SECRET_KEY!, {
});

export async function POST(request: NextRequest) {
    try {
        const { paymentLinkId, amount, branch, description, productName, tipAmount } = await request.json();

        if (!paymentLinkId || !amount || amount <= 0) {
            return NextResponse.json(
                { error: 'Payment link ID and valid amount are required' },
                { status: 400 }
            );
        }

        // Create payment intent with metadata
        const paymentIntent = await stripe.paymentIntents.create({
            amount: Math.round(amount * 100), // Convert to cents
            currency: 'usd',
            automatic_payment_methods: {
                enabled: true,
            },
            metadata: {
                paymentLinkId: paymentLinkId,
                branch: branch || 'unknown',
                location: branch || 'unknown', // Add location for easier filtering in Stripe
                productName: productName || '',
                description: description || '',
                tipAmount: tipAmount ? tipAmount.toString() : '0',
                source: 'payment_link', // Identify this as coming from your payment link system
            },
            description: `${productName || 'Payment'} - ${branch || 'Unknown Location'}${tipAmount > 0 ? ` (Tip: $${tipAmount.toFixed(2)})` : ''}`,
        });

        return NextResponse.json({
            clientSecret: paymentIntent.client_secret,
            paymentIntentId: paymentIntent.id,
        });
    } catch (error) {
        console.error('Error creating payment intent:', error);
        return NextResponse.json(
            { error: 'Failed to create payment intent' },
            { status: 500 }
        );
    }
}
